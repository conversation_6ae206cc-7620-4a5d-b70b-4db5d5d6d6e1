<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package Tracking - ZamSend</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/tracking.css">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="index.html">ZamSend</a>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="about.html">About</a></li>
                <li><a href="team.html">Team</a></li>
                <li><a href="faq.html">FAQ</a></li>
                <li><a href="tracking.html" class="active">Tracking</a></li>
                <li><a href="#contact" class="nav-cta">Contact</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-tracking">
        <div class="hero-background">
            <div class="tracking-animation">
                <div class="delivery-truck">
                    <svg width="60" height="40" viewBox="0 0 60 40" fill="none">
                        <rect x="5" y="15" width="30" height="15" rx="2" fill="white" opacity="0.8"/>
                        <rect x="35" y="20" width="15" height="10" rx="2" fill="white" opacity="0.8"/>
                        <circle cx="15" cy="35" r="4" fill="white" opacity="0.8"/>
                        <circle cx="45" cy="35" r="4" fill="white" opacity="0.8"/>
                    </svg>
                </div>
                <div class="tracking-dots">
                    <div class="dot dot-1"></div>
                    <div class="dot dot-2"></div>
                    <div class="dot dot-3"></div>
                    <div class="dot dot-4"></div>
                    <div class="dot dot-5"></div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-word">Track</span>
                    <span class="title-word">Your</span>
                    <span class="title-word highlight">Package</span>
                </h1>
                <p class="hero-subtitle">Real-time tracking with GPS precision and instant updates</p>
                
                <div class="tracking-form">
                    <div class="input-group">
                        <input type="text" id="tracking-number" placeholder="Enter your tracking number">
                        <button class="track-btn" id="track-btn">
                            <span class="btn-text">Track Package</span>
                            <div class="btn-loader"></div>
                        </button>
                    </div>
                    <p class="form-help">Example: ZS123456789UAE</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Tracking Results -->
    <section class="tracking-results" id="tracking-results" style="display: none;">
        <div class="container">
            <div class="results-header">
                <h2>Tracking Results</h2>
                <div class="package-info">
                    <div class="info-item">
                        <span class="label">Tracking Number:</span>
                        <span class="value" id="result-tracking-number">ZS123456789UAE</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Status:</span>
                        <span class="value status-badge" id="result-status">In Transit</span>
                    </div>
                    <div class="info-item">
                        <span class="label">Estimated Delivery:</span>
                        <span class="value" id="result-delivery">Today, 3:30 PM</span>
                    </div>
                </div>
            </div>

            <!-- Progress Timeline -->
            <div class="progress-timeline">
                <div class="timeline-item completed" data-aos="slide-right" data-aos-delay="100">
                    <div class="timeline-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="timeline-content">
                        <h4>Package Picked Up</h4>
                        <p>Your package has been collected from the sender</p>
                        <span class="timestamp">Today, 9:15 AM</span>
                    </div>
                </div>

                <div class="timeline-item completed" data-aos="slide-right" data-aos-delay="200">
                    <div class="timeline-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M20 6L9 17l-5-5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="timeline-content">
                        <h4>In Transit</h4>
                        <p>Package is on its way to the destination</p>
                        <span class="timestamp">Today, 11:30 AM</span>
                    </div>
                </div>

                <div class="timeline-item active" data-aos="slide-right" data-aos-delay="300">
                    <div class="timeline-icon">
                        <div class="pulse-ring"></div>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="timeline-content">
                        <h4>Out for Delivery</h4>
                        <p>Package is out for delivery and will arrive soon</p>
                        <span class="timestamp">Today, 2:45 PM</span>
                    </div>
                </div>

                <div class="timeline-item pending" data-aos="slide-right" data-aos-delay="400">
                    <div class="timeline-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <div class="timeline-content">
                        <h4>Delivered</h4>
                        <p>Package will be delivered to the recipient</p>
                        <span class="timestamp">Expected: Today, 3:30 PM</span>
                    </div>
                </div>
            </div>

            <!-- Live Map -->
            <div class="live-map" data-aos="fade-up" data-aos-delay="500">
                <div class="map-header">
                    <h3>Live Location</h3>
                    <div class="map-controls">
                        <button class="map-btn active" data-view="map">Map View</button>
                        <button class="map-btn" data-view="satellite">Satellite</button>
                    </div>
                </div>
                <div class="map-container">
                    <div class="map-placeholder">
                        <div class="delivery-route">
                            <div class="route-line"></div>
                            <div class="location-marker start">
                                <div class="marker-pulse"></div>
                                <span class="marker-label">Pickup Location</span>
                            </div>
                            <div class="location-marker current">
                                <div class="marker-pulse active"></div>
                                <span class="marker-label">Current Location</span>
                            </div>
                            <div class="location-marker end">
                                <div class="marker-pulse"></div>
                                <span class="marker-label">Delivery Address</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Package Details -->
            <div class="package-details" data-aos="fade-up" data-aos-delay="600">
                <h3>Package Details</h3>
                <div class="details-grid">
                    <div class="detail-item">
                        <span class="detail-label">Weight</span>
                        <span class="detail-value">2.5 kg</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Dimensions</span>
                        <span class="detail-value">30x20x15 cm</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Service Type</span>
                        <span class="detail-value">Express Delivery</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Delivery Instructions</span>
                        <span class="detail-value">Leave at front door</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tracking Features -->
    <section class="tracking-features">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2>Advanced Tracking Features</h2>
                <p>Experience the future of package tracking</p>
            </div>
            <div class="features-grid">
                <div class="feature-card" data-aos="zoom-in" data-aos-delay="100">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7l10 5 10-5-10-5z" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 17l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                            <path d="M2 12l10 5 10-5" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h4>Real-time GPS</h4>
                    <p>Track your package's exact location with precision GPS technology</p>
                </div>

                <div class="feature-card" data-aos="zoom-in" data-aos-delay="200">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h4>Instant Notifications</h4>
                    <p>Get real-time updates via SMS, email, and push notifications</p>
                </div>

                <div class="feature-card" data-aos="zoom-in" data-aos-delay="300">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="9" cy="9" r="2" stroke="currentColor" stroke-width="2"/>
                            <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h4>Photo Proof</h4>
                    <p>Receive photo confirmation when your package is delivered</p>
                </div>

                <div class="feature-card" data-aos="zoom-in" data-aos-delay="400">
                    <div class="feature-icon">
                        <svg width="40" height="40" viewBox="0 0 24 24" fill="none">
                            <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z" stroke="currentColor" stroke-width="2"/>
                        </svg>
                    </div>
                    <h4>Secure Tracking</h4>
                    <p>Your tracking information is protected with bank-level security</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Track Another Package CTA -->
    <section class="track-another-cta">
        <div class="container">
            <div class="cta-content" data-aos="zoom-in">
                <h2>Need to Track <span class="highlight">Another Package?</span></h2>
                <p>Enter another tracking number to get instant updates</p>
                <div class="cta-buttons">
                    <a href="#" class="btn btn-primary" onclick="scrollToTop()">Track Another</a>
                    <a href="index.html" class="btn btn-secondary">Send Package</a>
                </div>
            </div>
        </div>
    </section>

    <script src="assets/js/tracking.js"></script>
</body>
</html>
