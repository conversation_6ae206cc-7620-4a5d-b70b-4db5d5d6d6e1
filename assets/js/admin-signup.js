// ZamSend Admin Signup JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeSignupForm();
    initializePasswordToggles();
    initializePasswordValidation();
    initializeFormValidation();
    initializeSocialSignup();
});

// Initialize signup form
function initializeSignupForm() {
    const signupForm = document.getElementById('adminSignupForm');
    
    signupForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleSignup();
    });
}

// Handle signup process
function handleSignup() {
    if (!validateForm()) {
        return;
    }
    
    // Show loading
    showLoading();
    
    // Simulate API call delay
    setTimeout(() => {
        createAccount();
    }, 2000);
}

// Create account
function createAccount() {
    const formData = getFormData();
    
    // Simulate account creation
    try {
        // In a real application, this would make an API call
        console.log('Creating account with data:', formData);
        
        // Store pending account for demo purposes
        const pendingAccount = {
            ...formData,
            status: 'pending_approval',
            createdAt: new Date().toISOString(),
            id: generateAccountId()
        };
        
        // Store in localStorage for demo
        const pendingAccounts = JSON.parse(localStorage.getItem('zamsend_pending_accounts') || '[]');
        pendingAccounts.push(pendingAccount);
        localStorage.setItem('zamsend_pending_accounts', JSON.stringify(pendingAccounts));
        
        hideLoading();
        showSuccess();
        
    } catch (error) {
        hideLoading();
        showError('Failed to create account. Please try again.');
    }
}

// Get form data
function getFormData() {
    return {
        firstName: document.getElementById('firstName').value.trim(),
        lastName: document.getElementById('lastName').value.trim(),
        email: document.getElementById('email').value.trim().toLowerCase(),
        phone: document.getElementById('phone').value.trim(),
        password: document.getElementById('password').value // In real app, this would be hashed
    };
}

// Generate account ID
function generateAccountId() {
    return 'ACC-' + Date.now() + '-' + Math.random().toString(36).substr(2, 5).toUpperCase();
}

// Initialize social signup buttons
function initializeSocialSignup() {
    const googleBtn = document.getElementById('googleSignup');
    const githubBtn = document.getElementById('githubSignup');
    const facebookBtn = document.getElementById('facebookSignup');

    googleBtn.addEventListener('click', function() {
        handleSocialSignup('google', this);
    });

    githubBtn.addEventListener('click', function() {
        handleSocialSignup('github', this);
    });

    facebookBtn.addEventListener('click', function() {
        handleSocialSignup('facebook', this);
    });
}

// Handle social signup
function handleSocialSignup(provider, button) {
    // Add loading state
    button.classList.add('loading');
    button.disabled = true;

    // Simulate social authentication
    setTimeout(() => {
        // In a real application, this would integrate with OAuth providers
        simulateSocialAuth(provider, button);
    }, 1500);
}

// Simulate social authentication
function simulateSocialAuth(provider, button) {
    // Mock user data from social provider
    const mockSocialData = {
        google: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            profilePicture: 'https://via.placeholder.com/100'
        },
        github: {
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            profilePicture: 'https://via.placeholder.com/100'
        },
        facebook: {
            firstName: 'Mike',
            lastName: 'Johnson',
            email: '<EMAIL>',
            profilePicture: 'https://via.placeholder.com/100'
        }
    };

    const userData = mockSocialData[provider];

    // Create account with social data
    const socialAccount = {
        ...userData,
        provider: provider,
        status: 'pending_verification',
        createdAt: new Date().toISOString(),
        id: generateAccountId(),
        authMethod: 'social'
    };

    // Store account
    const pendingAccounts = JSON.parse(localStorage.getItem('zamsend_pending_accounts') || '[]');
    pendingAccounts.push(socialAccount);
    localStorage.setItem('zamsend_pending_accounts', JSON.stringify(pendingAccounts));

    // Remove loading state
    button.classList.remove('loading');
    button.disabled = false;

    // Show success
    showSocialSuccess(provider, userData);
}

// Show social signup success
function showSocialSuccess(provider, userData) {
    const successMessage = document.getElementById('successMessage');
    const successContent = successMessage.querySelector('.success-content');

    // Update success message for social signup
    successContent.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <h3>Account Created with ${provider.charAt(0).toUpperCase() + provider.slice(1)}!</h3>
        <p>Welcome ${userData.firstName}! Your account has been created successfully. Please check your email for verification instructions.</p>
        <button class="success-btn" onclick="window.location.href='admin-login.html'">
            Go to Login
        </button>
    `;

    successMessage.classList.add('active');
}

// Initialize password toggles
function initializePasswordToggles() {
    const passwordToggle = document.getElementById('passwordToggle');
    const confirmPasswordToggle = document.getElementById('confirmPasswordToggle');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    
    passwordToggle.addEventListener('click', function() {
        togglePasswordVisibility(passwordInput, this);
    });
    
    confirmPasswordToggle.addEventListener('click', function() {
        togglePasswordVisibility(confirmPasswordInput, this);
    });
}

// Toggle password visibility
function togglePasswordVisibility(input, button) {
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
    
    const icon = button.querySelector('i');
    icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
}

// Initialize password validation
function initializePasswordValidation() {
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    
    passwordInput.addEventListener('input', function() {
        validatePassword(this.value);
        validatePasswordMatch();
    });
    
    confirmPasswordInput.addEventListener('input', function() {
        validatePasswordMatch();
    });
}

// Validate password requirements
function validatePassword(password) {
    const requirements = {
        'length-req': password.length >= 8,
        'uppercase-req': /[A-Z]/.test(password),
        'lowercase-req': /[a-z]/.test(password),
        'number-req': /\d/.test(password),
        'special-req': /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    Object.keys(requirements).forEach(reqId => {
        const element = document.getElementById(reqId);
        if (requirements[reqId]) {
            element.classList.add('valid');
        } else {
            element.classList.remove('valid');
        }
    });
    
    return Object.values(requirements).every(req => req);
}

// Validate password match
function validatePasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const confirmPasswordInput = document.getElementById('confirmPassword');
    
    if (confirmPassword && password !== confirmPassword) {
        confirmPasswordInput.style.borderColor = '#ef4444';
        return false;
    } else if (confirmPassword) {
        confirmPasswordInput.style.borderColor = '#10b981';
        return true;
    } else {
        confirmPasswordInput.style.borderColor = '#e5e7eb';
        return false;
    }
}

// Initialize form validation
function initializeFormValidation() {
    const inputs = document.querySelectorAll('input[required], select[required]');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            if (this.classList.contains('error')) {
                validateField(this);
            }
        });
    });
    
    // Email validation
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
        validateEmail(this);
    });
    
    // Phone validation
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        formatPhoneNumber(this);
    });
}

// Validate individual field
function validateField(field) {
    const value = field.value.trim();
    
    if (!value) {
        showFieldError(field, 'This field is required');
        return false;
    }
    
    clearFieldError(field);
    return true;
}

// Validate email
function validateEmail(emailInput) {
    const email = emailInput.value.trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!email) {
        showFieldError(emailInput, 'Email is required');
        return false;
    }
    
    if (!emailRegex.test(email)) {
        showFieldError(emailInput, 'Please enter a valid email address');
        return false;
    }
    
    clearFieldError(emailInput);
    return true;
}

// Format phone number
function formatPhoneNumber(phoneInput) {
    let value = phoneInput.value.replace(/\D/g, '');
    
    if (value.startsWith('260')) {
        // Zambian format: +260 XX XXX XXXX
        value = value.replace(/(\d{3})(\d{2})(\d{3})(\d{4})/, '+$1 $2 $3 $4');
    } else if (value.length === 9) {
        // Local format: XXX XXX XXX
        value = value.replace(/(\d{3})(\d{3})(\d{3})/, '$1 $2 $3');
    }
    
    phoneInput.value = value;
}

// Show field error
function showFieldError(field, message) {
    field.classList.add('error');
    field.style.borderColor = '#ef4444';
    
    // Remove existing error message
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
    
    // Add error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
        color: #ef4444;
        font-size: 0.75rem;
        margin-top: 0.25rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    `;
    
    field.parentNode.appendChild(errorDiv);
}

// Clear field error
function clearFieldError(field) {
    field.classList.remove('error');
    field.style.borderColor = '#e5e7eb';
    
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// Validate entire form
function validateForm() {
    let isValid = true;
    
    // Validate required fields
    const requiredFields = document.querySelectorAll('input[required], select[required]');
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    // Validate email
    if (!validateEmail(document.getElementById('email'))) {
        isValid = false;
    }
    
    // Validate password
    const password = document.getElementById('password').value;
    if (!validatePassword(password)) {
        showFieldError(document.getElementById('password'), 'Password does not meet requirements');
        isValid = false;
    }
    
    // Validate password match
    if (!validatePasswordMatch()) {
        showFieldError(document.getElementById('confirmPassword'), 'Passwords do not match');
        isValid = false;
    }
    
    // Validate terms agreement
    const agreeTerms = document.getElementById('agreeTerms');
    if (!agreeTerms.checked) {
        showError('Please agree to the Terms of Service and Privacy Policy');
        isValid = false;
    }
    
    return isValid;
}

// Show loading overlay
function showLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.add('active');
}

// Hide loading overlay
function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.remove('active');
}

// Show success message
function showSuccess() {
    const successMessage = document.getElementById('successMessage');
    successMessage.classList.add('active');
}

// Show error message
function showError(message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'error-toast';
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 8px 30px rgba(239, 68, 68, 0.3);
        z-index: 10000;
        animation: slideInRight 0.3s ease;
        max-width: 400px;
    `;
    
    document.body.appendChild(toast);
    
    // Remove toast after 5 seconds
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 5000);
}

// Add CSS animations for toast
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .toast-content i {
        font-size: 1.2rem;
    }
`;
document.head.appendChild(style);
