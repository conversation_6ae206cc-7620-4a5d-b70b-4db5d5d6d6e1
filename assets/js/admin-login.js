// ZamSend Admin Login JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initializeLoginForm();
    initializePasswordToggle();
    initializeDemoCredentials();
    initializeSocialLogin();
});

// Development mode - disable 2FA for easier testing
const DEV_MODE = true;

// Demo user credentials
const demoUsers = {
    '<EMAIL>': {
        password: 'admin123',
        role: 'Super Admin',
        requiresTwoFactor: !DEV_MODE // Disabled in dev mode
    },
    '<EMAIL>': {
        password: 'manager123',
        role: 'Manager',
        requiresTwoFactor: !DEV_MODE // Disabled in dev mode
    },
    '<EMAIL>': {
        password: 'logistics123',
        role: 'Logistics',
        requiresTwoFactor: false
    }
};

// Initialize login form
function initializeLoginForm() {
    const loginForm = document.getElementById('adminLoginForm');
    
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        handleLogin();
    });
}

// Handle login process
function handleLogin() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    
    // Validate inputs
    if (!username || !password) {
        showError('Please enter both username and password');
        return;
    }
    
    // Show loading
    showLoading();
    
    // Simulate API call delay
    setTimeout(() => {
        authenticateUser(username, password);
    }, 1500);
}

// Authenticate user
function authenticateUser(username, password) {
    const user = demoUsers[username.toLowerCase()];
    
    if (!user || user.password !== password) {
        hideLoading();
        showError('Invalid username or password');
        return;
    }
    
    // Store user session
    sessionStorage.setItem('zamsend_admin_user', JSON.stringify({
        username: username,
        role: user.role,
        loginTime: new Date().toISOString()
    }));
    
    hideLoading();

    if (user.requiresTwoFactor && !DEV_MODE) {
        // Store pending 2FA data
        sessionStorage.setItem('zamsend_pending_2fa', JSON.stringify({
            username: username,
            role: user.role,
            timestamp: new Date().toISOString()
        }));

        // Redirect to 2FA page
        window.location.href = 'admin-2fa.html';
    } else {
        // Direct login in dev mode or for users without 2FA
        loginSuccess();
    }
}



// Login success
function loginSuccess() {
    const successMessage = document.getElementById('successMessage');
    successMessage.classList.add('active');
    
    // Redirect to admin panel after delay
    setTimeout(() => {
        window.location.href = 'admin/index.html';
    }, 2000);
}

// Initialize password toggle
function initializePasswordToggle() {
    const passwordToggle = document.getElementById('passwordToggle');
    const passwordInput = document.getElementById('password');
    
    passwordToggle.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        
        const icon = this.querySelector('i');
        icon.className = type === 'password' ? 'fas fa-eye' : 'fas fa-eye-slash';
    });
}

// Initialize demo credentials
function initializeDemoCredentials() {
    const demoAccounts = document.querySelectorAll('.demo-account');
    
    demoAccounts.forEach(account => {
        account.addEventListener('click', function() {
            const credentialText = this.querySelector('span').textContent;
            const [email, password] = credentialText.split(' / ');
            
            document.getElementById('username').value = email;
            document.getElementById('password').value = password;
            
            // Add visual feedback
            this.style.background = '#dbeafe';
            setTimeout(() => {
                this.style.background = '';
            }, 1000);
        });
    });
}

// Show loading overlay
function showLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.add('active');
}

// Hide loading overlay
function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.remove('active');
}

// Show error message
function showError(message) {
    // Remove existing error messages
    const existingError = document.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Create error message
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-message';
    errorDiv.innerHTML = `
        <div class="error-content">
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add error styles
    errorDiv.style.cssText = `
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        animation: shake 0.5s ease-in-out;
    `;
    
    // Insert before login button
    const loginBtn = document.querySelector('.login-btn');
    loginBtn.parentNode.insertBefore(errorDiv, loginBtn);
    
    // Remove error after 5 seconds
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 5000);
}

// Add shake animation
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
    
    .error-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .error-content i {
        color: #dc2626;
    }
`;
document.head.appendChild(style);

// Check if user is already logged in
function checkExistingSession() {
    const existingUser = sessionStorage.getItem('zamsend_admin_user');
    if (existingUser) {
        const user = JSON.parse(existingUser);
        const loginTime = new Date(user.loginTime);
        const now = new Date();
        const hoursDiff = (now - loginTime) / (1000 * 60 * 60);
        
        // Session expires after 8 hours
        if (hoursDiff < 8) {
            window.location.href = 'admin/index.html';
        } else {
            sessionStorage.removeItem('zamsend_admin_user');
        }
    }
}

// Initialize social login buttons
function initializeSocialLogin() {
    const googleBtn = document.getElementById('googleLogin');
    const githubBtn = document.getElementById('githubLogin');
    const facebookBtn = document.getElementById('facebookLogin');

    if (googleBtn) {
        googleBtn.addEventListener('click', function() {
            handleSocialLogin('google', this);
        });
    }

    if (githubBtn) {
        githubBtn.addEventListener('click', function() {
            handleSocialLogin('github', this);
        });
    }

    if (facebookBtn) {
        facebookBtn.addEventListener('click', function() {
            handleSocialLogin('facebook', this);
        });
    }
}

// Handle social login
function handleSocialLogin(provider, button) {
    // Add loading state
    button.classList.add('loading');
    button.disabled = true;

    // Simulate social authentication
    setTimeout(() => {
        authenticateSocialUser(provider, button);
    }, 1500);
}

// Authenticate social user
function authenticateSocialUser(provider, button) {
    // Mock social user data
    const mockSocialUsers = {
        google: {
            email: '<EMAIL>',
            name: 'Google User',
            role: 'User',
            provider: 'google'
        },
        github: {
            email: '<EMAIL>',
            name: 'GitHub User',
            role: 'User',
            provider: 'github'
        },
        facebook: {
            email: '<EMAIL>',
            name: 'Facebook User',
            role: 'User',
            provider: 'facebook'
        }
    };

    const userData = mockSocialUsers[provider];

    // Store user session
    sessionStorage.setItem('zamsend_admin_user', JSON.stringify({
        username: userData.email,
        name: userData.name,
        role: userData.role,
        provider: userData.provider,
        loginTime: new Date().toISOString(),
        authMethod: 'social'
    }));

    // Remove loading state
    button.classList.remove('loading');
    button.disabled = false;

    // Show success and redirect
    showSocialLoginSuccess(provider, userData);
}

// Show social login success
function showSocialLoginSuccess(provider, userData) {
    const successMessage = document.getElementById('successMessage');
    const successContent = successMessage.querySelector('.success-content');

    // Update success message for social login
    successContent.innerHTML = `
        <i class="fas fa-check-circle"></i>
        <h3>Welcome back, ${userData.name}!</h3>
        <p>Successfully signed in with ${provider.charAt(0).toUpperCase() + provider.slice(1)}. Redirecting to dashboard...</p>
    `;

    successMessage.classList.add('active');

    // Redirect to admin panel after delay
    setTimeout(() => {
        window.location.href = 'admin/index.html';
    }, 2000);
}

// Check session on page load
checkExistingSession();
