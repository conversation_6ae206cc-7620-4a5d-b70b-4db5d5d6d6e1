// Tracking Page - Modern Animations JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initTrackingForm();
    initScrollAnimations();
    initMapControls();
    initMobileMenu();
    initNavbarEffects();
});

// Tracking Form Functionality
function initTrackingForm() {
    const trackingInput = document.getElementById('tracking-number');
    const trackBtn = document.getElementById('track-btn');
    const trackingResults = document.getElementById('tracking-results');
    
    // Track button click handler
    trackBtn.addEventListener('click', handleTracking);
    
    // Enter key handler
    trackingInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            handleTracking();
        }
    });
    
    function handleTracking() {
        const trackingNumber = trackingInput.value.trim();
        
        if (!trackingNumber) {
            showError('Please enter a tracking number');
            return;
        }
        
        if (!isValidTrackingNumber(trackingNumber)) {
            showError('Please enter a valid tracking number (e.g., ZS123456789UAE)');
            return;
        }
        
        // Show loading state
        trackBtn.classList.add('loading');
        
        // Simulate API call
        setTimeout(() => {
            trackBtn.classList.remove('loading');
            showTrackingResults(trackingNumber);
        }, 2000);
    }
    
    function isValidTrackingNumber(number) {
        // Simple validation for demo purposes
        return number.length >= 8 && /^[A-Z]{2}\d+[A-Z]*$/i.test(number);
    }
    
    function showError(message) {
        // Create error message
        const existingError = document.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.style.cssText = `
            background: #fee2e2;
            color: #dc2626;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            text-align: center;
            animation: shake 0.5s ease-in-out;
        `;
        errorDiv.textContent = message;
        
        trackingInput.parentElement.parentElement.appendChild(errorDiv);
        
        // Remove error after 3 seconds
        setTimeout(() => {
            errorDiv.remove();
        }, 3000);
        
        // Add shake animation
        trackingInput.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            trackingInput.style.animation = '';
        }, 500);
    }
    
    function showTrackingResults(trackingNumber) {
        // Update tracking number in results
        document.getElementById('result-tracking-number').textContent = trackingNumber;
        
        // Show results section with animation
        trackingResults.style.display = 'block';
        trackingResults.style.opacity = '0';
        trackingResults.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            trackingResults.style.transition = 'all 0.8s ease';
            trackingResults.style.opacity = '1';
            trackingResults.style.transform = 'translateY(0)';
            
            // Scroll to results
            trackingResults.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
            
            // Animate timeline items
            animateTimelineItems();
        }, 100);
    }
}

// Animate timeline items
function animateTimelineItems() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    timelineItems.forEach((item, index) => {
        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
            
            // Add completion animation for completed items
            if (item.classList.contains('completed')) {
                const icon = item.querySelector('.timeline-icon');
                icon.style.transform = 'scale(1.2)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1)';
                }, 300);
            }
        }, index * 200);
    });
}

// Map Controls
function initMapControls() {
    const mapBtns = document.querySelectorAll('.map-btn');
    
    mapBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Remove active class from all buttons
            mapBtns.forEach(b => b.classList.remove('active'));
            
            // Add active class to clicked button
            btn.classList.add('active');
            
            // Add visual feedback
            btn.style.transform = 'scale(0.95)';
            setTimeout(() => {
                btn.style.transform = 'scale(1)';
            }, 150);
        });
    });
}

// Scroll-triggered Animations
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('[data-aos]');
    
    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationType = element.dataset.aos;
                const delay = element.dataset.aosDelay || 0;
                
                setTimeout(() => {
                    element.classList.add('aos-animate');
                    applyAnimation(element, animationType);
                }, delay);
                
                scrollObserver.unobserve(element);
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        scrollObserver.observe(element);
    });
}

// Apply specific animations
function applyAnimation(element, type) {
    switch(type) {
        case 'fade-up':
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
            break;
        case 'zoom-in':
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
            break;
        case 'slide-right':
            element.style.opacity = '1';
            element.style.transform = 'translateX(0)';
            break;
    }
}

// Mobile Menu Toggle
function initMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
}

// Navbar Scroll Effects
function initNavbarEffects() {
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });
}

// Scroll to top function
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
    
    // Reset form
    document.getElementById('tracking-number').value = '';
    document.getElementById('tracking-results').style.display = 'none';
}

// Initialize AOS-like animations with CSS
const style = document.createElement('style');
style.textContent = `
    [data-aos] {
        opacity: 0;
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    [data-aos="fade-up"] {
        transform: translateY(30px);
    }
    
    [data-aos="zoom-in"] {
        transform: scale(0.9);
    }
    
    [data-aos="slide-right"] {
        transform: translateX(-50px);
    }
    
    .aos-animate {
        opacity: 1 !important;
        transform: translateY(0) translateX(0) scale(1) !important;
    }
    
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);

// Advanced button interactions
document.querySelectorAll('.btn').forEach(button => {
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.05)';
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});

// Feature card hover effects
document.querySelectorAll('.feature-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        const icon = this.querySelector('.feature-icon');
        icon.style.transform = 'scale(1.1) rotate(5deg)';
    });
    
    card.addEventListener('mouseleave', function() {
        const icon = this.querySelector('.feature-icon');
        icon.style.transform = 'scale(1) rotate(0deg)';
    });
});

// Tracking input enhancements
const trackingInput = document.getElementById('tracking-number');
if (trackingInput) {
    trackingInput.addEventListener('input', function() {
        // Auto-format tracking number
        let value = this.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        this.value = value;
        
        // Add visual feedback for valid format
        if (value.length >= 8 && /^[A-Z]{2}\d+[A-Z]*$/.test(value)) {
            this.style.borderColor = '#43e97b';
            this.style.boxShadow = '0 0 0 3px rgba(67, 233, 123, 0.1)';
        } else {
            this.style.borderColor = '';
            this.style.boxShadow = '';
        }
    });
    
    trackingInput.addEventListener('focus', function() {
        this.parentElement.style.transform = 'scale(1.02)';
    });
    
    trackingInput.addEventListener('blur', function() {
        this.parentElement.style.transform = 'scale(1)';
    });
}

// Simulate real-time updates
function simulateRealTimeUpdates() {
    const statusBadge = document.getElementById('result-status');
    const currentMarker = document.querySelector('.location-marker.current');
    
    if (statusBadge && currentMarker) {
        setInterval(() => {
            // Animate current location marker
            currentMarker.style.transform = 'translateY(-50%) scale(1.1)';
            setTimeout(() => {
                currentMarker.style.transform = 'translateY(-50%) scale(1)';
            }, 500);
        }, 5000);
    }
}

// Performance optimization: Throttle scroll events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
const throttledScrollHandler = throttle(() => {
    // Additional scroll-based animations can be added here
}, 16);

window.addEventListener('scroll', throttledScrollHandler);

// Initialize real-time updates simulation
setTimeout(simulateRealTimeUpdates, 3000);
