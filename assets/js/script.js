// Hero-Header background slideshow
let currentSlide = 0;
const slides = document.querySelectorAll('.hero-header-background .hero-slide');
const totalSlides = slides.length;

function nextSlide() {
    // Remove active class from current slide
    slides[currentSlide].classList.remove('active');
    
    // Move to next slide
    currentSlide = (currentSlide + 1) % totalSlides;
    
    // Add active class to new slide
    slides[currentSlide].classList.add('active');
}

// Start slideshow when page loads
document.addEventListener('DOMContentLoaded', function() {
    if (slides.length > 0) {
        // Start auto-slideshow every 4 seconds
        setInterval(nextSlide, 4000);
    }
});

// Header and top bar scroll effects
let lastScrollTop = 0;

window.addEventListener('scroll', function() {
    const header = document.querySelector('header');
    const topBar = document.querySelector('.top-bar');
    const scrollPosition = window.scrollY;
    
    // Get all sections
    const sections = [
        { element: document.querySelector('.hero'), class: 'hero-section' },
        { element: document.querySelector('.clients'), class: 'clients-section' },
        { element: document.querySelector('.projects'), class: 'projects-section' },
        { element: document.querySelector('.what-we-do'), class: 'what-we-do-section' },
        { element: document.querySelector('.who-we-help'), class: 'who-we-help-section' },
        { element: document.querySelector('.contact'), class: 'contact-section' },
        { element: document.querySelector('.testimonials'), class: 'testimonials-section' }
    ];
    
    // Remove all section classes from header
    sections.forEach(section => {
        header.classList.remove(section.class);
    });
    
    // Find current section in view
    let currentSection = null;
    sections.forEach(section => {
        if (section.element) {
            const rect = section.element.getBoundingClientRect();
            const headerHeight = header.offsetHeight;
            
            // Check if section is in view (accounting for header height)
            if (rect.top <= headerHeight && rect.bottom > headerHeight) {
                currentSection = section.class;
            }
        }
    });
    
    // Apply current section class to header
    if (currentSection && scrollPosition > 100) {
        header.classList.add(currentSection);
        header.classList.add('scrolled');
    } else if (scrollPosition > 100) {
        header.classList.add('scrolled');
    } else {
        header.classList.remove('scrolled');
    }
    
    // Top bar hide/show effect
    if (scrollPosition > lastScrollTop && scrollPosition > 50) {
        // Scrolling down - hide top bar
        topBar.classList.add('hidden');
    } else if (scrollPosition < lastScrollTop) {
        // Scrolling up - show top bar
        topBar.classList.remove('hidden');
    }
    
    lastScrollTop = scrollPosition <= 0 ? 0 : scrollPosition; // For Mobile or negative scrolling
});

// Contact form validation and submission
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Clear previous errors
            clearErrors();
            
            // Validate form
            const isValid = validateForm();
            
            if (isValid) {
                // Show loading state
                const submitBtn = contactForm.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="loading-icon">⏳</i> Sending...';
                submitBtn.disabled = true;
                
                // Simulate form submission (replace with actual API call)
                setTimeout(() => {
                    // Show success message
                    showSuccessMessage();
                    
                    // Reset form
                    contactForm.reset();
                    
                    // Reset button
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 2000);
            }
        });
    }
    
    // Live chat functionality
    const chatBtn = document.querySelector('.chat-btn');
    if (chatBtn) {
        chatBtn.addEventListener('click', function() {
            // Simulate opening chat widget
            alert('Live chat feature would open here. This would typically integrate with a chat service like Intercom, Zendesk, or custom chat solution.');
        });
    }
    
    // Animate contact cards on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationDelay = `${Math.random() * 0.5}s`;
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe contact cards
    document.querySelectorAll('.contact-card, .office-card').forEach(card => {
        observer.observe(card);
    });
});

function validateForm() {
    let isValid = true;
    
    // Validate full name
    const fullName = document.getElementById('fullName');
    if (!fullName.value.trim()) {
        showError(fullName, 'Full name is required');
        isValid = false;
    } else if (fullName.value.trim().length < 2) {
        showError(fullName, 'Full name must be at least 2 characters');
        isValid = false;
    }
    
    // Validate email
    const email = document.getElementById('email');
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email.value.trim()) {
        showError(email, 'Email address is required');
        isValid = false;
    } else if (!emailRegex.test(email.value.trim())) {
        showError(email, 'Please enter a valid email address');
        isValid = false;
    }
    
    // Validate subject
    const subject = document.getElementById('subject');
    if (!subject.value.trim()) {
        showError(subject, 'Subject is required');
        isValid = false;
    } else if (subject.value.trim().length < 5) {
        showError(subject, 'Subject must be at least 5 characters');
        isValid = false;
    }
    
    // Validate message
    const message = document.getElementById('message');
    if (!message.value.trim()) {
        showError(message, 'Message is required');
        isValid = false;
    } else if (message.value.trim().length < 10) {
        showError(message, 'Message must be at least 10 characters');
        isValid = false;
    }
    
    return isValid;
}

function showError(input, message) {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    input.style.borderColor = '#ff6b6b';
    errorElement.textContent = message;
    errorElement.style.display = 'block';
    
    // Add shake animation
    input.style.animation = 'shake 0.5s ease-in-out';
    setTimeout(() => {
        input.style.animation = '';
    }, 500);
}

function clearErrors() {
    const errorMessages = document.querySelectorAll('.error-message');
    const inputs = document.querySelectorAll('.form-group input, .form-group textarea');
    
    errorMessages.forEach(error => {
        error.textContent = '';
        error.style.display = 'none';
    });
    
    inputs.forEach(input => {
        input.style.borderColor = 'rgba(255, 255, 255, 0.3)';
    });
}

function showSuccessMessage() {
    // Create success message element
    const successDiv = document.createElement('div');
    successDiv.className = 'success-message';
    successDiv.innerHTML = `
        <div class="success-content">
            <i class="success-icon">✅</i>
            <h4>Message Sent Successfully!</h4>
            <p>Thank you for contacting us. We'll get back to you within 2 hours during business hours.</p>
        </div>
    `;
    
    // Add styles
    successDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(79, 172, 254, 0.95);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        text-align: center;
        z-index: 10000;
        backdrop-filter: blur(10px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        animation: successFadeIn 0.5s ease-out;
    `;
    
    document.body.appendChild(successDiv);
    
    // Remove after 4 seconds
    setTimeout(() => {
        successDiv.style.animation = 'successFadeOut 0.5s ease-out';
        setTimeout(() => {
            document.body.removeChild(successDiv);
        }, 500);
    }, 4000);
}

// Add CSS animations for form validation
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-5px); }
        75% { transform: translateX(5px); }
    }
    
    @keyframes successFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }
        to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
    }
    
    @keyframes successFadeOut {
        from {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }
        to {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }
    }
    
    .animate-in {
        animation: fadeInUp 0.6s ease forwards;
    }
`;
document.head.appendChild(style);

// For future JavaScript interactivity
console.log("ZamSend website script loaded.");