// ZamSend Admin Panel JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Check authentication first
    if (!checkAuthentication()) {
        return;
    }

    // Initialize all components
    initializeCounters();
    initializeCharts();
    initializeSidebar();
    initializeNotifications();
    initializeQuickActions();
    initializeUserProfile();
});

// Check if user is authenticated
function checkAuthentication() {
    const user = sessionStorage.getItem('zamsend_admin_user');

    if (!user) {
        // Redirect to login page
        window.location.href = 'login.html';
        return false;
    }

    try {
        const userData = JSON.parse(user);
        const loginTime = new Date(userData.loginTime);
        const now = new Date();
        const hoursDiff = (now - loginTime) / (1000 * 60 * 60);

        // Session expires after 8 hours
        if (hoursDiff >= 8) {
            sessionStorage.removeItem('zamsend_admin_user');
            alert('Your session has expired. Please log in again.');
            window.location.href = 'login.html';
            return false;
        }

        // Update user profile display
        updateUserProfile(userData);
        return true;

    } catch (error) {
        console.error('Error parsing user data:', error);
        sessionStorage.removeItem('zamsend_admin_user');
        window.location.href = 'login.html';
        return false;
    }
}

// Update user profile in header
function updateUserProfile(userData) {
    const profileName = document.querySelector('.profile-name');
    const profileRole = document.querySelector('.profile-role');

    if (profileName && profileRole) {
        profileName.textContent = userData.username.split('@')[0];
        profileRole.textContent = userData.role;
    }
}

// Animated Counter Function
function animateCounter(element, target, duration = 2000) {
    let start = 0;
    const increment = target / (duration / 16);
    
    function updateCounter() {
        start += increment;
        if (start < target) {
            element.textContent = Math.floor(start);
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = target;
        }
    }
    updateCounter();
}

// Initialize Counters
function initializeCounters() {
    const counters = document.querySelectorAll('.kpi-number[data-target]');
    
    // Intersection Observer for animation trigger
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.dataset.target);
                animateCounter(entry.target, target);
                observer.unobserve(entry.target);
            }
        });
    });
    
    counters.forEach(counter => observer.observe(counter));
}

// Initialize Charts
function initializeCharts() {
    // Shipments Overview Chart
    const shipmentsCtx = document.getElementById('shipmentsChart');
    if (shipmentsCtx) {
        new Chart(shipmentsCtx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Shipments',
                    data: [120, 190, 300, 500, 200, 300, 450],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#f1f5f9'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });
    }

    // Delivery Modes Chart
    const deliveryModesCtx = document.getElementById('deliveryModesChart');
    if (deliveryModesCtx) {
        new Chart(deliveryModesCtx, {
            type: 'doughnut',
            data: {
                labels: ['Express', 'Standard', 'Same-Day', 'International'],
                datasets: [{
                    data: [35, 40, 15, 10],
                    backgroundColor: [
                        '#3b82f6',
                        '#10b981',
                        '#f59e0b',
                        '#8b5cf6'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
}

// Sidebar Functionality
function initializeSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        mainContent.classList.toggle('expanded');
    });

    // Menu item active state
    const menuItems = document.querySelectorAll('.menu-item a');
    menuItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all items
            document.querySelectorAll('.menu-item').forEach(menuItem => {
                menuItem.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.parentElement.classList.add('active');
            
            // Update page title
            const pageTitle = this.querySelector('span').textContent;
            document.querySelector('.header-left h1').textContent = pageTitle;
        });
    });
}

// Notifications
function initializeNotifications() {
    const notificationBtn = document.querySelector('.notification-btn');
    
    notificationBtn.addEventListener('click', function() {
        // Simulate notification dropdown
        alert('Notifications:\n\n• New shipment requires attention\n• System backup completed\n• 3 pending support tickets');
    });
}

// Quick Actions
function initializeQuickActions() {
    const actionButtons = document.querySelectorAll('.action-btn');
    
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.querySelector('span').textContent;
            
            // Add loading state
            this.style.opacity = '0.7';
            this.style.pointerEvents = 'none';
            
            // Simulate action
            setTimeout(() => {
                this.style.opacity = '1';
                this.style.pointerEvents = 'auto';
                
                // Show success message based on action
                showSuccessMessage(action);
            }, 1500);
        });
    });
}

// Success Messages
function showSuccessMessage(action) {
    const messages = {
        'Add Shipment': 'New shipment form opened successfully!',
        'Add User': 'User registration form is ready!',
        'Generate Report': 'Report generation started. You\'ll be notified when ready.',
        'Update Pricing': 'Pricing configuration panel opened!',
        'Send Notification': 'Notification composer is ready!',
        'System Backup': 'System backup initiated successfully!'
    };
    
    const message = messages[action] || 'Action completed successfully!';
    
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        box-shadow: 0 8px 30px rgba(16, 185, 129, 0.3);
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // Remove toast after 3 seconds
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Add CSS animations for toast
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .toast-content i {
        font-size: 1.2rem;
    }
`;
document.head.appendChild(style);

// Real-time Updates Simulation
function simulateRealTimeUpdates() {
    setInterval(() => {
        // Update active shipments randomly
        const activeShipmentsElement = document.querySelector('.active-shipments .kpi-number');
        if (activeShipmentsElement) {
            const currentValue = parseInt(activeShipmentsElement.textContent);
            const change = Math.floor(Math.random() * 10) - 5; // Random change between -5 and +5
            const newValue = Math.max(0, currentValue + change);
            activeShipmentsElement.textContent = newValue;
        }
        
        // Update notification badge
        const notificationBadge = document.querySelector('.notification-badge');
        if (notificationBadge) {
            const currentCount = parseInt(notificationBadge.textContent);
            const newCount = Math.max(0, currentCount + Math.floor(Math.random() * 3) - 1);
            notificationBadge.textContent = newCount;
            
            if (newCount === 0) {
                notificationBadge.style.display = 'none';
            } else {
                notificationBadge.style.display = 'block';
            }
        }
    }, 30000); // Update every 30 seconds
}

// Start real-time updates
simulateRealTimeUpdates();

// Search Functionality
document.querySelector('.search-box input').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    
    if (searchTerm.length > 2) {
        // Simulate search results
        console.log('Searching for:', searchTerm);
        
        // In a real application, this would make an API call
        // and display search results in a dropdown
    }
});

// Initialize user profile functionality
function initializeUserProfile() {
    const userProfile = document.querySelector('.user-profile');
    if (userProfile) {
        userProfile.addEventListener('click', function() {
            showProfileDropdown();
        });
    }
}

// Show profile dropdown
function showProfileDropdown() {
    const userProfile = document.querySelector('.user-profile');

    // Remove existing dropdown
    const existingDropdown = document.querySelector('.profile-dropdown');
    if (existingDropdown) {
        existingDropdown.remove();
        return;
    }

    // Create dropdown
    const dropdown = document.createElement('div');
    dropdown.className = 'profile-dropdown';
    dropdown.innerHTML = `
        <div class="dropdown-item" onclick="showProfileSettings()">
            <i class="fas fa-user"></i>
            <span>Profile Settings</span>
        </div>
        <div class="dropdown-item" onclick="showAccountSettings()">
            <i class="fas fa-cog"></i>
            <span>Account Settings</span>
        </div>
        <div class="dropdown-item" onclick="showSecuritySettings()">
            <i class="fas fa-shield-alt"></i>
            <span>Security</span>
        </div>
        <div class="dropdown-divider"></div>
        <div class="dropdown-item logout" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
        </div>
    `;

    // Add dropdown styles
    dropdown.style.cssText = `
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 12px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        padding: 0.5rem;
        min-width: 200px;
        z-index: 1000;
        margin-top: 0.5rem;
    `;

    userProfile.style.position = 'relative';
    userProfile.appendChild(dropdown);

    // Remove dropdown when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.user-profile')) {
                if (dropdown.parentNode) {
                    dropdown.parentNode.removeChild(dropdown);
                }
            }
        });
    }, 100);
}

// Logout function
function logout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear session
        sessionStorage.removeItem('zamsend_admin_user');

        // Show logout message
        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logging out...</span>
            </div>
        `;

        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(239, 68, 68, 0.3);
            z-index: 10000;
            animation: slideInRight 0.3s ease;
        `;

        document.body.appendChild(toast);

        // Redirect to login page
        setTimeout(() => {
            window.location.href = 'login.html';
        }, 1000);
    }
}

// Placeholder functions for dropdown items
function showProfileSettings() {
    alert('Profile Settings - Feature coming soon!');
}

function showAccountSettings() {
    alert('Account Settings - Feature coming soon!');
}

function showSecuritySettings() {
    alert('Security Settings - Feature coming soon!');
}
