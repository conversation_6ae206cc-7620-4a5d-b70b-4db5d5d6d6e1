// FAQ Page - Modern Animations JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initFAQAccordion();
    initCategoryTabs();
    initSearchFunctionality();
    initScrollAnimations();
    initMobileMenu();
    initNavbarEffects();
});

// FAQ Accordion Functionality
function initFAQAccordion() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');
            
            // Close all other FAQ items
            faqItems.forEach(otherItem => {
                if (otherItem !== item) {
                    otherItem.classList.remove('active');
                }
            });
            
            // Toggle current item
            if (isActive) {
                item.classList.remove('active');
            } else {
                item.classList.add('active');
                
                // Add a subtle bounce animation
                item.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    item.style.transform = 'scale(1)';
                }, 200);
            }
        });
    });
}

// Category Tab Switching
function initCategoryTabs() {
    const categoryBtns = document.querySelectorAll('.category-btn');
    const faqCategories = document.querySelectorAll('.faq-category');
    
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const targetCategory = btn.dataset.category;
            
            // Remove active class from all buttons and categories
            categoryBtns.forEach(button => button.classList.remove('active'));
            faqCategories.forEach(category => category.classList.remove('active'));
            
            // Add active class to clicked button and corresponding category
            btn.classList.add('active');
            document.getElementById(targetCategory).classList.add('active');
            
            // Reset all FAQ items to closed state
            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => item.classList.remove('active'));
            
            // Animate FAQ items in the new category
            animateFAQItems(targetCategory);
        });
    });
}

// Animate FAQ items when category changes
function animateFAQItems(categoryId) {
    const activeCategory = document.getElementById(categoryId);
    const faqItems = activeCategory.querySelectorAll('.faq-item');
    
    faqItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            item.style.transition = 'all 0.6s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Search Functionality
function initSearchFunctionality() {
    const searchInput = document.getElementById('faq-search');
    const searchBtn = document.querySelector('.search-btn');
    
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        const allFaqItems = document.querySelectorAll('.faq-item');
        let hasResults = false;
        
        // Remove existing no-results message
        const existingNoResults = document.querySelector('.no-results');
        if (existingNoResults) {
            existingNoResults.remove();
        }
        
        allFaqItems.forEach(item => {
            const question = item.querySelector('.faq-question h3').textContent.toLowerCase();
            const answer = item.querySelector('.faq-answer p').textContent.toLowerCase();
            
            if (searchTerm === '' || question.includes(searchTerm) || answer.includes(searchTerm)) {
                item.classList.remove('hidden');
                item.classList.remove('highlight');
                
                if (searchTerm !== '' && (question.includes(searchTerm) || answer.includes(searchTerm))) {
                    item.classList.add('highlight');
                    hasResults = true;
                }
                
                if (searchTerm === '') {
                    hasResults = true;
                }
            } else {
                item.classList.add('hidden');
            }
        });
        
        // Show no results message if needed
        if (!hasResults && searchTerm !== '') {
            showNoResults();
        }
        
        // Show all categories if searching, otherwise show active category
        const faqCategories = document.querySelectorAll('.faq-category');
        if (searchTerm !== '') {
            faqCategories.forEach(category => {
                category.style.display = 'block';
            });
        } else {
            faqCategories.forEach(category => {
                if (category.classList.contains('active')) {
                    category.style.display = 'block';
                } else {
                    category.style.display = 'none';
                }
            });
        }
    }
    
    function showNoResults() {
        const faqContent = document.querySelector('.faq-content .container');
        const noResultsDiv = document.createElement('div');
        noResultsDiv.className = 'no-results';
        noResultsDiv.innerHTML = `
            <h3>No results found</h3>
            <p>We couldn't find any FAQs matching your search. Try different keywords or browse our categories above.</p>
        `;
        faqContent.appendChild(noResultsDiv);
    }
    
    // Search on input
    searchInput.addEventListener('input', performSearch);
    
    // Search on button click
    searchBtn.addEventListener('click', performSearch);
    
    // Search on Enter key
    searchInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
}

// Scroll-triggered Animations
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('[data-aos]');
    
    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationType = element.dataset.aos;
                const delay = element.dataset.aosDelay || 0;
                
                setTimeout(() => {
                    element.classList.add('aos-animate');
                    applyAnimation(element, animationType);
                }, delay);
                
                scrollObserver.unobserve(element);
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        scrollObserver.observe(element);
    });
}

// Apply specific animations
function applyAnimation(element, type) {
    switch(type) {
        case 'fade-up':
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
            break;
        case 'zoom-in':
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
            break;
    }
}

// Mobile Menu Toggle
function initMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
}

// Navbar Scroll Effects
function initNavbarEffects() {
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Initialize AOS-like animations with CSS
const style = document.createElement('style');
style.textContent = `
    [data-aos] {
        opacity: 0;
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    [data-aos="fade-up"] {
        transform: translateY(30px);
    }
    
    [data-aos="zoom-in"] {
        transform: scale(0.9);
    }
    
    .aos-animate {
        opacity: 1 !important;
        transform: translateY(0) scale(1) !important;
    }
`;
document.head.appendChild(style);

// Advanced button interactions
document.querySelectorAll('.btn').forEach(button => {
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.05)';
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});

// Category button ripple effect
document.querySelectorAll('.category-btn').forEach(button => {
    button.addEventListener('click', function(e) {
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        this.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
});

// Add ripple effect CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .category-btn {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);

// FAQ item hover effects
document.querySelectorAll('.faq-item').forEach(item => {
    item.addEventListener('mouseenter', function() {
        if (!this.classList.contains('active')) {
            this.style.transform = 'translateY(-5px) scale(1.01)';
        }
    });
    
    item.addEventListener('mouseleave', function() {
        if (!this.classList.contains('active')) {
            this.style.transform = 'translateY(0) scale(1)';
        }
    });
});

// Performance optimization: Throttle scroll events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
const throttledScrollHandler = throttle(() => {
    // Additional scroll-based animations can be added here
}, 16);

window.addEventListener('scroll', throttledScrollHandler);
