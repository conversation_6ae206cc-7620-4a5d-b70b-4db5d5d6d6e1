// Team Page - Modern Animations JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all animations and interactions
    initTabSwitching();
    initScrollAnimations();
    initHoverEffects();
    initParallaxEffects();
    initMobileMenu();
    initNavbarEffects();
});

// Tab Switching Functionality
function initTabSwitching() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetTab = button.dataset.tab;
            
            // Remove active class from all buttons and contents
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Add active class to clicked button and corresponding content
            button.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
            
            // Trigger animation for team members in the active tab
            animateTeamMembers(targetTab);
        });
    });
}

// Animate team members when tab switches
function animateTeamMembers(tabId) {
    const activeTab = document.getElementById(tabId);
    const teamMembers = activeTab.querySelectorAll('.team-member');
    
    teamMembers.forEach((member, index) => {
        member.style.opacity = '0';
        member.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            member.style.transition = 'all 0.6s ease';
            member.style.opacity = '1';
            member.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Scroll-triggered Animations
function initScrollAnimations() {
    const animatedElements = document.querySelectorAll('[data-aos]');
    
    const scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationType = element.dataset.aos;
                const delay = element.dataset.aosDelay || 0;
                
                setTimeout(() => {
                    element.classList.add('aos-animate');
                    applyAnimation(element, animationType);
                }, delay);
                
                scrollObserver.unobserve(element);
            }
        });
    }, { threshold: 0.1 });
    
    animatedElements.forEach(element => {
        scrollObserver.observe(element);
    });
}

// Apply specific animations
function applyAnimation(element, type) {
    switch(type) {
        case 'fade-up':
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
            break;
        case 'scale-in':
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
            break;
        case 'flip-up':
            element.style.opacity = '1';
            element.style.transform = 'rotateY(0)';
            break;
        case 'slide-up':
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
            break;
        case 'zoom-in':
            element.style.opacity = '1';
            element.style.transform = 'scale(1)';
            break;
    }
}

// Advanced Hover Effects
function initHoverEffects() {
    // Leader cards 3D tilt effect
    const leaderCards = document.querySelectorAll('.leader-card');
    
    leaderCards.forEach(card => {
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 15;
            const rotateY = (centerX - x) / 15;
            
            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-10px) scale(1.02)`;
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateY(0) scale(1)';
        });
    });
    
    // Team member cards magnetic effect
    const teamMembers = document.querySelectorAll('.team-member');
    
    teamMembers.forEach(member => {
        member.addEventListener('mousemove', (e) => {
            const rect = member.getBoundingClientRect();
            const x = e.clientX - rect.left - rect.width / 2;
            const y = e.clientY - rect.top - rect.height / 2;
            
            member.style.transform = `translateX(${x * 0.05}px) translateY(${y * 0.05}px) translateY(-8px) scale(1.02)`;
        });
        
        member.addEventListener('mouseleave', () => {
            member.style.transform = 'translateX(0) translateY(0) scale(1)';
        });
    });
    
    // Skill tags wave animation on hover
    const skillTags = document.querySelectorAll('.member-skills span');
    
    skillTags.forEach((tag, index) => {
        tag.addEventListener('mouseenter', () => {
            // Create wave effect on neighboring tags
            const parentSkills = tag.parentElement;
            const allTags = parentSkills.querySelectorAll('span');
            
            allTags.forEach((otherTag, otherIndex) => {
                const distance = Math.abs(index - otherIndex);
                const delay = distance * 50;
                
                setTimeout(() => {
                    otherTag.style.transform = 'translateY(-3px) scale(1.05)';
                    setTimeout(() => {
                        otherTag.style.transform = 'translateY(0) scale(1)';
                    }, 200);
                }, delay);
            });
        });
    });
}

// Parallax Effects
function initParallaxEffects() {
    const gridItems = document.querySelectorAll('.grid-item');
    
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.3;
        
        gridItems.forEach((item, index) => {
            const speed = (index + 1) * 0.05;
            item.style.transform = `translateY(${rate * speed}px) scale(${1 + speed})`;
        });
    });
}

// Mobile Menu Toggle
function initMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
}

// Navbar Scroll Effects
function initNavbarEffects() {
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', () => {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
        } else {
            navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            navbar.style.boxShadow = 'none';
        }
    });
}

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Initialize AOS-like animations with CSS
const style = document.createElement('style');
style.textContent = `
    [data-aos] {
        opacity: 0;
        transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    [data-aos="fade-up"] {
        transform: translateY(30px);
    }
    
    [data-aos="scale-in"] {
        transform: scale(0.8);
    }
    
    [data-aos="flip-up"] {
        transform: rotateY(90deg);
    }
    
    [data-aos="slide-up"] {
        transform: translateY(30px);
    }
    
    [data-aos="zoom-in"] {
        transform: scale(0.9);
    }
    
    .aos-animate {
        opacity: 1 !important;
        transform: translateY(0) translateX(0) scale(1) rotateY(0) !important;
    }
`;
document.head.appendChild(style);

// Advanced button interactions
document.querySelectorAll('.btn').forEach(button => {
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.05)';
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
    
    button.addEventListener('mousedown', function() {
        this.style.transform = 'translateY(-1px) scale(0.98)';
    });
    
    button.addEventListener('mouseup', function() {
        this.style.transform = 'translateY(-3px) scale(1.05)';
    });
});

// Tab button ripple effect
document.querySelectorAll('.tab-btn').forEach(button => {
    button.addEventListener('click', function(e) {
        const ripple = document.createElement('span');
        const rect = this.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        this.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    });
});

// Add ripple effect CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .tab-btn {
        position: relative;
        overflow: hidden;
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);

// Performance optimization: Throttle scroll events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
const throttledScrollHandler = throttle(() => {
    // Additional scroll-based animations can be added here
}, 16);

window.addEventListener('scroll', throttledScrollHandler);

// Initialize team member animations on page load
setTimeout(() => {
    animateTeamMembers('engineering');
}, 500);
