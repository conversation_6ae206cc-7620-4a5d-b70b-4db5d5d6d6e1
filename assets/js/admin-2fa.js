// ZamSend Admin Two-Factor Authentication JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Check if user should be on this page
    checkTwoFactorAccess();
    
    // Initialize components
    initializeCodeInputs();
    initializeVerifyButton();
    initializeBackupCode();
    initializeResendCode();
    
    // Load user information
    loadUserInfo();
});

// Check if user has access to 2FA page
function checkTwoFactorAccess() {
    const pendingAuth = sessionStorage.getItem('zamsend_pending_2fa');
    
    if (!pendingAuth) {
        // Redirect to login if no pending 2FA
        window.location.href = 'admin-login.html';
        return;
    }
    
    try {
        const authData = JSON.parse(pendingAuth);
        const authTime = new Date(authData.timestamp);
        const now = new Date();
        const minutesDiff = (now - authTime) / (1000 * 60);
        
        // 2FA session expires after 10 minutes
        if (minutesDiff > 10) {
            sessionStorage.removeItem('zamsend_pending_2fa');
            alert('Two-factor authentication session expired. Please log in again.');
            window.location.href = 'admin-login.html';
        }
    } catch (error) {
        console.error('Error parsing 2FA data:', error);
        window.location.href = 'admin-login.html';
    }
}

// Load user information
function loadUserInfo() {
    const pendingAuth = sessionStorage.getItem('zamsend_pending_2fa');
    
    if (pendingAuth) {
        try {
            const authData = JSON.parse(pendingAuth);
            document.getElementById('userEmail').textContent = authData.username;
            document.getElementById('userRole').textContent = authData.role;
        } catch (error) {
            console.error('Error loading user info:', error);
        }
    }
}

// Initialize code inputs
function initializeCodeInputs() {
    const codeInputs = document.querySelectorAll('.code-input');
    
    codeInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            const value = e.target.value;
            
            // Only allow numbers
            if (!/^\d$/.test(value)) {
                e.target.value = '';
                return;
            }
            
            // Add filled class
            e.target.classList.add('filled');
            
            // Move to next input
            if (value && index < codeInputs.length - 1) {
                codeInputs[index + 1].focus();
            }
            
            // Check if all inputs are filled
            checkCodeComplete();
        });
        
        input.addEventListener('keydown', function(e) {
            // Handle backspace
            if (e.key === 'Backspace') {
                if (!e.target.value && index > 0) {
                    codeInputs[index - 1].focus();
                    codeInputs[index - 1].value = '';
                    codeInputs[index - 1].classList.remove('filled');
                } else {
                    e.target.classList.remove('filled');
                }
                checkCodeComplete();
            }
            
            // Handle Enter key
            if (e.key === 'Enter') {
                e.preventDefault();
                const verifyBtn = document.getElementById('verifyBtn');
                if (!verifyBtn.disabled) {
                    verifyTwoFactorCode();
                }
            }
        });
        
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const paste = e.clipboardData.getData('text');
            const digits = paste.replace(/\D/g, '').slice(0, 6);
            
            digits.split('').forEach((digit, i) => {
                if (codeInputs[i]) {
                    codeInputs[i].value = digit;
                    codeInputs[i].classList.add('filled');
                }
            });
            
            // Focus last filled input or next empty one
            const lastIndex = Math.min(digits.length - 1, 5);
            if (lastIndex >= 0) {
                codeInputs[lastIndex].focus();
            }
            
            checkCodeComplete();
        });
    });
}

// Check if all code inputs are complete
function checkCodeComplete() {
    const codeInputs = document.querySelectorAll('.code-input');
    const verifyBtn = document.getElementById('verifyBtn');
    
    const allFilled = Array.from(codeInputs).every(input => input.value.length === 1);
    
    verifyBtn.disabled = !allFilled;
    
    if (allFilled) {
        // Auto-verify after short delay
        setTimeout(() => {
            verifyTwoFactorCode();
        }, 500);
    }
}

// Initialize verify button
function initializeVerifyButton() {
    const verifyBtn = document.getElementById('verifyBtn');
    const twoFactorForm = document.getElementById('twoFactorForm');
    
    twoFactorForm.addEventListener('submit', function(e) {
        e.preventDefault();
        verifyTwoFactorCode();
    });
    
    verifyBtn.addEventListener('click', function(e) {
        e.preventDefault();
        verifyTwoFactorCode();
    });
}

// Verify two-factor code
function verifyTwoFactorCode() {
    const codeInputs = document.querySelectorAll('.code-input');
    const code = Array.from(codeInputs).map(input => input.value).join('');
    
    if (code.length !== 6) {
        showError('Please enter the complete 6-digit code');
        return;
    }
    
    // Show loading
    showLoading();
    
    // Simulate verification delay
    setTimeout(() => {
        hideLoading();
        
        // For demo, accept any 6-digit code or specific test codes
        const validCodes = ['123456', '000000', '111111'];
        
        if (validCodes.includes(code) || /^\d{6}$/.test(code)) {
            // Complete authentication
            completeAuthentication();
        } else {
            showError('Invalid verification code. Please try again.');
            clearCodeInputs();
        }
    }, 1500);
}

// Complete authentication process
function completeAuthentication() {
    const pendingAuth = sessionStorage.getItem('zamsend_pending_2fa');
    
    if (pendingAuth) {
        try {
            const authData = JSON.parse(pendingAuth);
            
            // Store completed authentication
            sessionStorage.setItem('zamsend_admin_user', JSON.stringify({
                username: authData.username,
                role: authData.role,
                loginTime: new Date().toISOString(),
                twoFactorVerified: true
            }));
            
            // Remove pending 2FA
            sessionStorage.removeItem('zamsend_pending_2fa');
            
            // Show success and redirect
            showSuccess();
            
        } catch (error) {
            console.error('Error completing authentication:', error);
            showError('Authentication error. Please try again.');
        }
    }
}

// Initialize backup code functionality
function initializeBackupCode() {
    const useBackupCode = document.getElementById('useBackupCode');
    const backToCode = document.getElementById('backToCode');
    const backupSection = document.getElementById('backupSection');
    const twoFactorForm = document.getElementById('twoFactorForm');
    const backupVerifyBtn = document.getElementById('backupVerifyBtn');
    
    useBackupCode.addEventListener('click', function(e) {
        e.preventDefault();
        twoFactorForm.style.display = 'none';
        backupSection.style.display = 'block';
        document.getElementById('backupCode').focus();
    });
    
    backToCode.addEventListener('click', function(e) {
        e.preventDefault();
        backupSection.style.display = 'none';
        twoFactorForm.style.display = 'block';
        document.querySelector('.code-input').focus();
    });
    
    backupVerifyBtn.addEventListener('click', function() {
        verifyBackupCode();
    });
    
    document.getElementById('backupCode').addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            verifyBackupCode();
        }
    });
}

// Verify backup code
function verifyBackupCode() {
    const backupCode = document.getElementById('backupCode').value.trim();
    
    if (!backupCode) {
        showError('Please enter a backup code');
        return;
    }
    
    showLoading();
    
    // Simulate verification
    setTimeout(() => {
        hideLoading();
        
        // For demo, accept any 8-character alphanumeric code
        if (backupCode.length >= 6) {
            completeAuthentication();
        } else {
            showError('Invalid backup code. Please check and try again.');
            document.getElementById('backupCode').value = '';
        }
    }, 1000);
}

// Initialize resend code functionality
function initializeResendCode() {
    const resendBtn = document.getElementById('resendCode');
    
    resendBtn.addEventListener('click', function() {
        resendVerificationCode();
    });
}

// Resend verification code
function resendVerificationCode() {
    const resendBtn = document.getElementById('resendCode');
    const originalHTML = resendBtn.innerHTML;
    
    resendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    resendBtn.disabled = true;
    
    setTimeout(() => {
        resendBtn.innerHTML = '<i class="fas fa-check"></i> Code Sent!';
        
        setTimeout(() => {
            resendBtn.innerHTML = originalHTML;
            resendBtn.disabled = false;
        }, 2000);
    }, 1500);
}

// Clear code inputs
function clearCodeInputs() {
    const codeInputs = document.querySelectorAll('.code-input');
    codeInputs.forEach(input => {
        input.value = '';
        input.classList.remove('filled');
    });
    codeInputs[0].focus();
    checkCodeComplete();
}

// Show loading overlay
function showLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.add('active');
}

// Hide loading overlay
function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    loadingOverlay.classList.remove('active');
}

// Show success message
function showSuccess() {
    const successMessage = document.getElementById('successMessage');
    successMessage.classList.add('active');
    
    // Redirect to admin panel after delay
    setTimeout(() => {
        window.location.href = 'admin.html';
    }, 2000);
}

// Show error message
function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    const errorClose = document.getElementById('errorClose');
    
    errorText.textContent = message;
    errorMessage.classList.add('active');
    
    errorClose.addEventListener('click', function() {
        errorMessage.classList.remove('active');
    });
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
        errorMessage.classList.remove('active');
    }, 5000);
}
