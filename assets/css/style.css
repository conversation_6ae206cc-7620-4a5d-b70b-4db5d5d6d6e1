* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

/* General Body Styles */
body {
    font-family: sans-serif;
    margin: 0;
    background-color: #f9f9f9;
    color: #333;
}

/* Hero-Header Background Styles */
.hero-header-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: -2;
}

.hero-header-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, rgba(8, 27, 41, 0.8), rgba(8, 27, 41, 0.6));
    z-index: -1;
}

.hero-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 15.5s ease-in-out;
}

.hero-slide.active {
    opacity: 1;
}

/* Top Bar Styles */
.top-bar {
    background: transparent;
    padding: 0.75rem 5%;
    font-size: 0.9rem;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 255, 136, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1000;
}

.top-bar.hidden {
    transform: translateY(-100%);
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.contact-info {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.contact-info a {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.3s ease;
    font-weight: 500;
}

.contact-info a:hover {
    color: #00b7ffff;
    text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.phone-icon {
    font-size: 1.3rem;
    opacity: 0.9;
}

.email-icon {
    font-size: 2rem;
    opacity: 0.9;
}

.social-icons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: transparent;
    border-radius: 50%;
    color: #ffffff;
    text-decoration: none;
    font-size: 1.1rem;
    font-weight: bold;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 2px dotted rgb(0, 132, 255);
}

.social-icon:hover {
    background: transparent;
    background-color: rgba(38, 0, 255, 0.781);
    color: #5cffff;
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.facebook-icon {
    font-family: Arial, sans-serif;
    font-weight: bold;
}

.twitter-icon {
    font-size: 1rem;
}

.whatsapp-icon {
    font-size: 1rem;
}

.instagram-icon {
    font-size: 1rem;
}

/* Header Styles */
header {
    background: transparent;
    padding: 2rem 5%;
    position: sticky;
    top: 0;
    left: 0;
    z-index: 1001;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

header.scrolled {
    background: linear-gradient(135deg, rgba(5, 59, 59, 0.97), rgba(14, 74, 85, 0.9));
    backdrop-filter: blur(0.01px);
    padding: 1.5rem 5%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Dynamic header colors for different sections */
header.clients-section {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9));
}

header.projects-section {
    background: linear-gradient(135deg, rgba(240, 147, 251, 0.9), rgba(245, 87, 108, 0.9));
}

header.what-we-do-section {
    background: linear-gradient(135deg, rgba(79, 172, 254, 0.9), rgba(0, 242, 254, 0.9));
}

header.who-we-help-section {
    background: linear-gradient(135deg, rgba(67, 233, 123, 0.9), rgba(56, 249, 215, 0.9));
}

header.contact-section {
    background: linear-gradient(135deg, rgba(250, 112, 154, 0.9), rgba(254, 225, 64, 0.9));
}

header.testimonials-section {
    background: linear-gradient(135deg, rgba(168, 237, 234, 0.9), rgba(254, 214, 227, 0.9));
}

nav {
    display: flex;
    margin: 0;
    justify-content: space-between;
    align-items: center;
}

.logo a {
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none;
    color: #ffffff;
    transition: color 0.3s ease;
}

header.scrolled .logo a {
    color: #ffffffff;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

nav ul li a {
    text-decoration: none;
    color: #ffffff;
    font-weight: 500;
    transition: color 0.3s ease;
}

header.scrolled nav ul li a {
    color: #ffffffff;
}

nav ul li a:hover {
    color: #fffb00ff;
}

nav a.active {
    color: #00abf0;
}

.btn {
    background-color: #0011ff;
    color: #fff;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

header.scrolled .btn {
    background-color: #0011ff;
    color: #fff;
}

.btn:hover {
    background-color: #555;
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

nav .btn {
    background: #3b82f6;
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.995rem;
}

nav .btn:hover {
    background: #d8581dff;
    transform: scale(1.05);
}


/* Hero Section Styles */
.hero {
    position: relative;
    height: calc(100vh - 6rem);
    padding: 6rem 5% 12rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-content {
    position: relative;
    z-index: 100;
    max-width: 800px;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
    text-align: center;
}

.hero-content p {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto 2rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-buttons .btn {
    margin: 0 0.5rem;
}

.btn-secondary {
    background-color: transparent;
    color: #ffffff;
    border: 2px solid #ffffff;
}

.btn-secondary:hover {
    background-color: #ffffff;
    color: #081b29;
}

/* Clients Section Styles */
.clients {
    padding: 4rem 5%;
    text-align: center;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: #ffffff;
}

.clients h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.client-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4rem;
    filter: none;
    opacity: 1;
}

.client-logos span {
    font-size: 1.2rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* Delivery Services Section Styles */
.delivery-services {
    padding: 5rem 5%;
    text-align: center;
    background: #f8f9fa;
    color: #333;
    position: relative;
    overflow: hidden;
}

.delivery-services::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02), rgba(147, 197, 253, 0.02));
    z-index: 1;
}

.delivery-services .container {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
}

.delivery-services h2 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: #1f2937;
    font-weight: 700;
    line-height: 1.2;
}

.highlight-blue {
    color: #3b82f6;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.delivery-services > .container > p {
    color: #6b7280;
    max-width: 700px;
    margin: 0 auto 4rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
    padding: 0 1rem;
}

.delivery-services .service-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 2.5rem 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    text-align: left;
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.delivery-services .service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.delivery-services .service-card:hover::before {
    transform: scaleX(1);
}

.delivery-services .service-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.15);
    border-color: #3b82f6;
}

.delivery-services .service-card:nth-child(1) { animation-delay: 0.1s; }
.delivery-services .service-card:nth-child(2) { animation-delay: 0.2s; }
.delivery-services .service-card:nth-child(3) { animation-delay: 0.3s; }
.delivery-services .service-card:nth-child(4) { animation-delay: 0.4s; }
.delivery-services .service-card:nth-child(5) { animation-delay: 0.5s; }

.delivery-services .service-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
}

.express-delivery .service-icon {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: #ffffff;
}

.standard-delivery .service-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
}

.same-day-delivery .service-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
}

.international-shipping .service-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
}

.special-handling .service-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
}

.delivery-services .service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.delivery-services .service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
    color: #1f2937;
    font-weight: 600;
}

.delivery-services .service-card > p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.5;
}

.service-features {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    line-height: 1.4;
    color: #4b5563;
}

.checkmark {
    color: #10b981;
    font-weight: bold;
    margin-right: 0.75rem;
    font-size: 1rem;
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.service-features strong {
    color: #1f2937;
    font-weight: 600;
}

.services-cta {
    text-align: center;
    background: #ffffff;
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    max-width: 800px;
    margin: 0 auto;
}

.services-cta h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #1f2937;
    font-weight: 600;
    line-height: 1.3;
}

.services-cta p {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.services-btn {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.services-btn:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.arrow-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.services-btn:hover .arrow-icon {
    transform: translateX(3px);
}

/* New Services Section Styles */
.services-section {
    background: #f8fafc;
    padding: 5rem 5%;
    text-align: center;
}

.services-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #1f2937;
    font-weight: 700;
}

.services-section > .container > p {
    color: #6b7280;
    max-width: 700px;
    margin: 0 auto 4rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

.services-grid-new {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 4rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.service-card-new {
    background: #ffffff;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    text-align: left;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-card-new:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.service-card-new::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
}

.service-icon-new {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.service-card-new h3 {
    font-size: 1.4rem;
    margin-bottom: 0.8rem;
    color: #1f2937;
    font-weight: 600;
}

.service-card-new > p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
    line-height: 1.5;
}

.service-features-new {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features-new li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.8rem;
    font-size: 0.9rem;
    line-height: 1.4;
    color: #4b5563;
}

.service-features-new .checkmark {
    color: #10b981;
    font-weight: bold;
    margin-right: 0.8rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.service-features-new strong {
    color: #1f2937;
    font-weight: 600;
}

.services-cta-new {
    text-align: center;
    background: #ffffff;
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e5e7eb;
    max-width: 800px;
    margin: 0 auto;
}

.services-cta-new h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: #1f2937;
    font-weight: 600;
    line-height: 1.3;
}

.services-cta-new p {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.6;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.services-btn-new {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: #ffffff;
    padding: 0.9rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    font-size: 1rem;
}

.services-btn-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.services-btn-new .arrow-icon {
    transition: transform 0.3s ease;
}

.services-btn-new:hover .arrow-icon {
    transform: translateX(3px);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.service-card {
    background-color: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.service-icon {
    width: 60px;
    height: 60px;
    background-color: #007bff;
    border-radius: 50%;
    margin: 0 auto 1rem;
    /* Placeholder for icon */
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.service-card p {
    color: #666;
}

/* Who We Help Section Styles */
.who-we-help {
    padding: 4rem 5%;
    text-align: center;
    background: linear-gradient(135deg, #43e97b, #38f9d7);
    color: #ffffff;
}

.who-we-help h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.who-we-help > .container > p {
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin: 0 auto 3rem;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.help-card {
    background-color: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.help-icon {
    width: 100px;
    height: 100px;
    background-color: #f0f2f5;
    border-radius: 50%;
    margin: 0 auto 1rem;
    /* Placeholder for icon */
}

.help-card h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Contact Section Styles */
.contact {
    background: #f8fafc;
    padding: 5rem 5%;
    color: #333;
}

.contact-header {
    text-align: center;
    margin-bottom: 4rem;
}

.contact-header h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #1f2937;
    font-weight: 700;
}

.contact-header .highlight-blue {
    color: #3b82f6;
}

.contact-header p {
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Contact Methods */
.contact-methods {
    margin-bottom: 4rem;
}

.contact-methods h3 {
    text-align: center;
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: #1f2937;
    font-weight: 600;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.contact-card {
    background: #ffffff;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.contact-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: #d1d5db;
}

.contact-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: #ffffff;
}

.contact-icon.green {
    background: #10b981;
}

.contact-icon.blue {
    background: #3b82f6;
}

.contact-icon.purple {
    background: #8b5cf6;
}

.contact-card h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #1f2937;
    font-weight: 600;
}

.contact-card p {
    color: #6b7280;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    line-height: 1.4;
}

.contact-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.contact-link:hover {
    color: #1d4ed8;
}

.chat-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #10b981;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-btn {
    background: #3b82f6;
    color: #ffffff;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.chat-btn:hover {
    background: #1d4ed8;
    transform: scale(1.05);
}

/* Operating Hours */
.operating-hours {
    text-align: center;
    margin-bottom: 4rem;
    background: #ffffff;
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.hours-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #3b82f6;
    animation: rotate 4s linear infinite;
}

.operating-hours h3 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #2c3e50;
    font-weight: 600;
}

.hours-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.hours-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid #e5e7eb;
}

.hours-card h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.hours-card p {
    color: #6b7280;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

/* Global Offices */
.global-offices {
    margin-bottom: 4rem;
}

.global-offices h3 {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.global-offices > p {
    text-align: center;
    color: #6b7280;
    max-width: 600px;
    margin: 0 auto 3rem;
    line-height: 1.6;
}

.offices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.office-card {
    background: #ffffff;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.office-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.office-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #3b82f6;
}

.office-card h4 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.office-card p {
    color: #6b7280;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.office-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.office-link:hover {
    color: #1d4ed8;
}

.office-note {
    text-align: center;
    color: #6b7280;
    margin-top: 2rem;
}

.contact-online {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
}

.contact-online:hover {
    color: #1d4ed8;
}

/* Contact Form */
.contact-form-section {
    background: #ffffff;
    padding: 2.5rem;
    border-radius: 16px;
    border: 1px solid #e5e7eb;
    margin-bottom: 4rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.contact-form-section h3 {
    text-align: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #1f2937;
    font-weight: 600;
}

.contact-form-section > p {
    text-align: center;
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.6;
    font-size: 0.875rem;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: #374151;
    font-weight: 600;
    font-size: 0.875rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 10px;
    background: #ffffff;
    color: #374151;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: #9ca3af;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.error-message {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: block;
}

.btn-primary {
    background: #3b82f6;
    color: #ffffff;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin: 2rem auto 0;
}

.btn-primary:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3);
}

/* Final Contact CTA */
.contact-cta-final {
    text-align: center;
    background: #3b82f6;
    padding: 2.5rem;
    border-radius: 16px;
    color: #ffffff;
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.contact-cta-final h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
    font-weight: 600;
    text-align: center;
}

.contact-cta-final p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    max-width: 450px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    font-size: 0.875rem;
    text-align: center;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
}

.cta-buttons .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    min-width: 150px;
}

.cta-buttons .btn-primary {
    background: #ffffff;
    color: #3b82f6;
    border: 1px solid #ffffff;
}

.cta-buttons .btn-primary:hover {
    background: #f8fafc;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.cta-buttons .btn-secondary {
    background: transparent;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.cta-buttons .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #ffffff;
    transform: translateY(-1px);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Testimonials Section Styles */
.testimonials {
    padding: 4rem 5%;
    text-align: center;
    background: linear-gradient(135deg, #a8edea, #fed6e3);
    color: #333;
}

.testimonials h2 {
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.testimonial {
    max-width: 700px;
    margin: 0 auto;
}

.testimonial p {
    font-size: 1.5rem;
    font-style: italic;
    color: #555;
    margin-bottom: 2rem;
    position: relative;
    padding: 0 2rem;
}

.testimonial p::before,
.testimonial p::after {
    content: '“';
    font-size: 5rem;
    color: #e0e0e0;
    position: absolute;
}

.testimonial p::before {
    top: -2rem;
    left: -1rem;
}

.testimonial p::after {
    content: '”';
    bottom: -4rem;
    right: -1rem;
}

.author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
}

.author h4 {
    margin: 0;
    font-size: 1.2rem;
}

.author span {
    color: #777;
}

/* Footer Styles */
footer {
    background-color: #333;
    color: #f4f4f4;
    padding: 3rem 0;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 0 5%;
}

.footer-section h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.footer-section.about p {
    line-height: 1.6;
}

.footer-section.links ul {
    list-style: none;
    padding: 0;
}

.footer-section.links ul a {
    color: #f4f4f4;
    text-decoration: none;
    display: block;
    margin-bottom: 0.8rem;
    transition: color 0.3s ease;
}

.footer-section.links ul a:hover {
    color: #007bff;
}

.contact-input {
    background: #444;
    color: #f4f4f4;
    border: 1px solid #555;
    width: 100%;
    padding: 0.8rem;
    margin-bottom: 1rem;
    border-radius: 5px;
}

.footer-bottom {
    text-align: center;
    padding: 1.5rem 0;
    border-top: 1px solid #444;
    margin-top: 2rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .top-bar {
        padding: 0.5rem 5%;
        font-size: 0.8rem;
    }

    .top-bar-content {
        flex-direction: column;
        gap: 0.75rem;
    }

    .contact-info {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .social-icons {
        gap: 0.75rem;
    }

    .social-icon {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    nav {
        flex-direction: column;
        gap: 1rem;
    }

    nav ul {
        flex-direction: column;
        text-align: center;
        width: 100%;
        gap: 1rem;
    }

    .hero {
        padding: 4rem 5% 8rem;
    }

    .hero-content h1 {
        font-size: 2.5rem;
        line-height: 1.1;
    }

    .hero-content p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .hero-buttons {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .hero-buttons .btn {
        margin: 0;
        width: 200px;
        text-align: center;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-card {
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .hours-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .offices-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-form-section {
        padding: 2rem 1.5rem;
    }

    .contact-cta-final {
        padding: 2rem 1.5rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
        width: 100%;
    }

    .cta-buttons .btn {
        width: 200px;
        justify-content: center;
        min-width: 200px;
    }

    .contact-header h2 {
        font-size: 2rem;
    }

    .contact-methods h3,
    .operating-hours h3,
    .global-offices h3,
    .contact-form-section h3 {
        font-size: 1.25rem;
    }

    .hours-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-section.links ul {
        display: inline-block;
        text-align: left;
    }

    /* New Services Section Responsive */
    .services-grid-new {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card-new {
        padding: 1.5rem;
    }

    .services-cta-new {
        padding: 2rem 1.5rem;
    }

    .services-section {
        padding: 3rem 5%;
    }

    .services-section h2 {
        font-size: 2rem;
    }
}
