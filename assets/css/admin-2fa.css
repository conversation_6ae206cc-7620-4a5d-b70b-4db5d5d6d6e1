/* ZamSend Admin Two-Factor Authentication Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Background Elements */
.tfa-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 8s ease-in-out infinite;
}

.shape-1 {
    width: 150px;
    height: 150px;
    top: 15%;
    left: 15%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    right: 10%;
    animation-delay: 3s;
}

.shape-3 {
    width: 120px;
    height: 120px;
    bottom: 25%;
    left: 25%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

/* Two-Factor Container */
.tfa-container {
    width: 100%;
    max-width: 480px;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

/* Two-Factor Card */
.tfa-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 3rem 2.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: slideUp 0.8s ease;
    text-align: center;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header */
.tfa-header {
    margin-bottom: 2.5rem;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.logo i {
    font-size: 2rem;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo span {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1a202c;
}

.security-icon {
    margin-bottom: 1rem;
}

.security-icon i {
    font-size: 3rem;
    color: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.tfa-header h1 {
    font-size: 1.8rem;
    color: #1a202c;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.tfa-header p {
    color: #6b7280;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1.5rem;
}

/* User Info */
.user-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    background: #f8fafc;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.user-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #10b981, #059669);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.user-details {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.user-email {
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
}

.user-role {
    color: #6b7280;
    font-size: 0.8rem;
}

/* Code Section */
.code-section {
    margin-bottom: 2rem;
}

.code-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #374151;
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1rem;
}

.code-label i {
    color: #10b981;
}

.code-inputs {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.code-input {
    width: 60px;
    height: 60px;
    text-align: center;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1.5rem;
    font-weight: 700;
    background: #f9fafb;
    color: #1f2937;
    transition: all 0.3s ease;
}

.code-input:focus {
    outline: none;
    border-color: #10b981;
    background: white;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    transform: scale(1.05);
}

.code-input.filled {
    border-color: #10b981;
    background: #ecfdf5;
    color: #065f46;
}

.code-help {
    text-align: center;
}

.code-help p {
    color: #6b7280;
    font-size: 0.85rem;
}

/* Verify Button */
.verify-btn {
    width: 100%;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.verify-btn:disabled {
    background: #d1d5db;
    cursor: not-allowed;
    transform: none;
}

.verify-btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
}

.verify-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.verify-btn:not(:disabled):hover::before {
    left: 100%;
}

/* Alternative Options */
.alternative-options {
    border-top: 1px solid #e5e7eb;
    padding-top: 2rem;
}

.resend-section {
    margin-bottom: 1.5rem;
}

.resend-section p {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

.resend-btn {
    background: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.resend-btn:hover {
    border-color: #10b981;
    color: #10b981;
    background: #f0fdf4;
}

.backup-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    align-items: center;
}

.backup-link, .help-link {
    color: #10b981;
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.backup-link:hover, .help-link:hover {
    color: #059669;
}

/* Backup Section */
.backup-section {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.backup-header {
    margin-bottom: 2rem;
}

.backup-header i {
    font-size: 2.5rem;
    color: #f59e0b;
    margin-bottom: 1rem;
}

.backup-header h3 {
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.backup-header p {
    color: #6b7280;
    font-size: 0.9rem;
}

.backup-input-group {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.backup-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 1rem;
    background: #f9fafb;
    text-align: center;
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
}

.backup-input:focus {
    outline: none;
    border-color: #f59e0b;
    background: white;
    box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.backup-verify-btn {
    background: #f59e0b;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.backup-verify-btn:hover {
    background: #d97706;
    transform: translateY(-1px);
}

.backup-help {
    font-size: 0.85rem;
    color: #6b7280;
    line-height: 1.5;
}

.backup-help a {
    color: #10b981;
    text-decoration: none;
    font-weight: 500;
    margin-top: 1rem;
    display: inline-block;
}

.backup-help a:hover {
    color: #059669;
}

/* Security Info */
.security-info {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.security-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
    flex: 1;
}

.security-item i {
    color: #10b981;
    font-size: 1.2rem;
}

.security-item span {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

/* Footer */
.tfa-footer {
    text-align: center;
    margin-top: 2rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.85rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #10b981;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.success-message.active {
    opacity: 1;
    visibility: visible;
}

.success-content {
    background: white;
    padding: 3rem 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: successPop 0.5s ease;
}

@keyframes successPop {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.success-content i {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.success-content h3 {
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.success-content p {
    color: #6b7280;
}

/* Error Message */
.error-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.error-message.active {
    opacity: 1;
    visibility: visible;
}

.error-content {
    background: white;
    padding: 3rem 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: errorShake 0.5s ease;
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

.error-content i {
    font-size: 4rem;
    color: #ef4444;
    margin-bottom: 1rem;
}

.error-content h3 {
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.error-content p {
    color: #6b7280;
    margin-bottom: 2rem;
}

.error-close {
    background: #ef4444;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.error-close:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .tfa-container {
        padding: 1rem;
    }

    .tfa-card {
        padding: 2rem 1.5rem;
    }

    .code-inputs {
        gap: 0.5rem;
    }

    .code-input {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .security-info {
        flex-direction: column;
        gap: 1.5rem;
    }

    .security-item {
        flex-direction: row;
        justify-content: center;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.75rem;
    }

    .backup-input-group {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .code-inputs {
        gap: 0.25rem;
    }

    .code-input {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .user-info {
        flex-direction: column;
        gap: 0.75rem;
    }

    .user-details {
        align-items: center;
    }
}
