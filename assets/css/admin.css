/* ZamSend Admin Panel Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #f8fafc;
    color: #2d3748;
    line-height: 1.6;
}

/* Sidebar Navigation */
.sidebar {
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #1a202c 0%, #2d3748 100%);
    color: white;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: #3b82f6;
}

.logo i {
    font-size: 2rem;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-menu {
    list-style: none;
    padding: 1rem 0;
}

.menu-item {
    scroll-behavior: smooth;
    margin-bottom: 0.5rem;
}

.menu-item a {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-item a:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-left-color: #3b82f6;
}

.menu-item.active a {
    background: rgba(59, 130, 246, 0.2);
    color: white;
    border-left-color: #3b82f6;
}

.menu-item i {
    width: 20px;
    text-align: center;
    font-size: 1.1rem;
}

/* Main Content */
.main-content {
    margin-left: 280px;
    min-height: 100vh;
    background: #f8fafc;
}

/* Top Header */
.top-header {
    background: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid #e2e8f0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #4a5568;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: #f7fafc;
    color: #3b82f6;
}

.header-left h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a202c;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box i {
    position: absolute;
    left: 1rem;
    color: #a0aec0;
}

.search-box input {
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 25px;
    background: #f7fafc;
    width: 300px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.notifications-dropdown {
    position: relative;
}

.notification-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #4a5568;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    position: relative;
}

.notification-btn:hover {
    background: #f7fafc;
    color: #3b82f6;
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: #ef4444;
    color: white;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.user-profile:hover {
    background: #f7fafc;
}

.profile-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
}

.profile-info {
    display: flex;
    flex-direction: column;
}

.profile-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: #1a202c;
}

.profile-role {
    font-size: 0.75rem;
    color: #718096;
}

/* Dashboard Content */
.dashboard-content {
    padding: 2rem;
}

/* KPI Section */
.kpi-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.kpi-card {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.kpi-card.active-shipments {
    border-left-color: #3b82f6;
}

.kpi-card.delivered-today {
    border-left-color: #10b981;
}

.kpi-card.avg-delivery {
    border-left-color: #f59e0b;
}

.kpi-card.revenue {
    border-left-color: #8b5cf6;
}

.kpi-card.failed-deliveries {
    border-left-color: #ef4444;
}

.kpi-card.satisfaction {
    border-left-color: #06b6d4;
}

.kpi-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.active-shipments .kpi-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.delivered-today .kpi-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.avg-delivery .kpi-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.revenue .kpi-icon {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.failed-deliveries .kpi-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.satisfaction .kpi-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.kpi-info {
    flex: 1;
}

.kpi-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
    margin-bottom: 0.25rem;
}

.kpi-label {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.kpi-change {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

.kpi-change.positive {
    background: #dcfce7;
    color: #166534;
}

.kpi-change.negative {
    background: #fef2f2;
    color: #991b1b;
}

/* Analytics Section */
.analytics-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.chart-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.chart-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chart-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
}

.chart-subtitle {
    color: #718096;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.chart-controls select {
    padding: 0.5rem 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    background: white;
    color: #4a5568;
    font-size: 0.875rem;
}

.chart-body {
    padding: 2rem;
    height: 300px;
}

.shipments-chart .chart-body {
    height: 400px;
}

/* Regional Map */
.regional-map {
    display: flex;
    flex-direction: column;
}

.map-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    height: fit-content;
}

.map-body {
    padding: 2rem;
}

.map-placeholder {
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    color: #718096;
}

.map-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #a0aec0;
}

.map-stats {
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.region-stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.region-name {
    font-weight: 600;
    color: #1a202c;
}

.region-count {
    color: #3b82f6;
    font-weight: 500;
    font-size: 0.875rem;
}

/* Activity Section */
.activity-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.activity-card, .actions-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
}

.activity-header, .actions-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.activity-header h3, .actions-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1a202c;
}

.actions-subtitle {
    color: #718096;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.view-all {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

.view-all:hover {
    color: #1d4ed8;
}

.activity-list {
    padding: 1.5rem 2rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #f7fafc;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.activity-icon.new-shipment {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.activity-icon.delivered {
    background: linear-gradient(135deg, #10b981, #059669);
}

.activity-icon.user-added {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.activity-icon.price-update {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.activity-details {
    flex: 1;
}

.activity-text {
    color: #4a5568;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-time {
    color: #a0aec0;
    font-size: 0.75rem;
}

/* Quick Actions */
.actions-grid {
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #4a5568;
}

.action-btn:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.action-btn i {
    font-size: 1.5rem;
}

.action-btn span {
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .analytics-section {
        grid-template-columns: 1fr;
    }

    .activity-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .main-content {
        margin-left: 0;
    }

    .top-header {
        padding: 1rem;
    }

    .header-right {
        gap: 1rem;
    }

    .search-box input {
        width: 200px;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .kpi-section {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .kpi-card {
        padding: 1.5rem;
    }

    .actions-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.kpi-card {
    animation: fadeInUp 0.6s ease forwards;
}

.kpi-card:nth-child(1) { animation-delay: 0.1s; }
.kpi-card:nth-child(2) { animation-delay: 0.2s; }
.kpi-card:nth-child(3) { animation-delay: 0.3s; }
.kpi-card:nth-child(4) { animation-delay: 0.4s; }
.kpi-card:nth-child(5) { animation-delay: 0.5s; }
.kpi-card:nth-child(6) { animation-delay: 0.6s; }
