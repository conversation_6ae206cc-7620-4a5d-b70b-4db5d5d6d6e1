@import url('httpa://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

/* Page Theme*/
body {
    color: #fff;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 60px 10%;
    background: transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1001;
}

/* Logo */
.logo {
    position: relative;
    font-size: 25px;
    color: #fff;
    text-decoration: none;
    font-weight: 600;
}

.logo::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 105%;
    height: 100%;
    background: #081b29;
    animation: showRight 1s ease forwards;
    animation-delay: .8s;
}

/* Navigation Bar*/
.navbar {
    margin: 0;
}
.navbar a {
    position: relative;
    font-size: 18px;
    color: #fff;
    text-decoration: none;
    font-weight: 500;
    margin-left: 35px;
    transition: .5s;
}

.navbar a::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 155%;
    height: 100%;
    background: #081b29;
    animation: showRight 2s ease forwards;
    animation-delay: 1.6s;
}

.navbar a:hover, .navbar a.active {
    color: #00abf0;
}

/* Home */
.home {
    
    background: #081b29;
    background-size: cover;
    background-position: center;
    height: 100vh;
    display: flex;
    align-items: center;
    padding: 0 10%;
}

.home-content {
    margin-top: 16em;
    max-width: 600px;
}

.home-content h1 {
    position: relative;
    font-size: 46px;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.5);
}

.home-content h1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #081b29;
    animation: showLeft 5s ease forwards;
    animation-delay: 2.5s;
}

.home-content h3 {
    position: relative;
    font-size: 26px;
    font-weight: 700;
    color: #00abf0;
}

.home-content h3::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: #081b29;
    animation: showRight 5s ease forwards;
    animation-delay: 6.9s;
}

.home-content p {
    color: rgba(255, 251, 10, 0.781);
    position: relative;
    font-size: 15px;
    line-height: 1.5;
    margin: 20px 0 40px;
}

.home-content p::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #081b29;
    animation: showLeft 5s ease forwards;
    animation-delay: 10.8s;
}

/* Home Button */
.home-content .btn-box {
    position: relative;
    display: flex;
    justify-content: space-between;
    width: 345px;
    height: 50px;
}

.home-content .btn-box::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: #081b29;
    animation: showRight 5s ease forwards;
    animation-delay: 16.9s;
    z-index: 2;
}

.btn-box a {
    position: relative;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 150px;
    height: 100%;
    background: #00abf0;
    border: 2px solid #00abf0;
    border-radius: 8px;
    font-size: 19px;
    color: #081b29;
    text-decoration: none;
    font-weight: 600;
    letter-spacing: 1px;
    z-index: 1;
    overflow: hidden;
    transition: .5s;
}

.btn-box a:hover {
    color: #00abf0;
}

.btn-box a:nth-child(2) {
    background: transparent;
    color: #00abf0;
}

.btn-box a:nth-child(2):hover {
    color: #081b29;
}
.btn-box a:nth-child(2)::before {
    background: #00abf0;
}
.btn-box a::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: #081b29;
    z-index: -1;
    transition: .5s;
}
.btn-box a:hover::before {
    width: 100%;
}

/* KEYFRAMES ANIMATION */
@keyframes showRight {
    100% {
        width: 0;
    }
}

@keyframes showLeft {
    100% {
        width: 0;
    }
}
