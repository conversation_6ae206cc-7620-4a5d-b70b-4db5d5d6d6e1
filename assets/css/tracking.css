/* Tracking Page - Modern Animations */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background: #fafbfc;
  color: #333;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation - Same as other pages */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.nav-logo a {
  font-size: 2rem;
  font-weight: 800;
  color: #667eea;
  text-decoration: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-menu a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
  width: 100%;
}

.nav-cta {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  padding: 12px 24px;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.nav-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Hero Section */
.hero-tracking {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  z-index: -2;
}

.tracking-animation {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.delivery-truck {
  position: absolute;
  top: 20%;
  left: -100px;
  animation: truckMove 15s linear infinite;
}

@keyframes truckMove {
  0% {
    left: -100px;
    transform: translateY(0);
  }
  25% {
    left: 25%;
    transform: translateY(-10px);
  }
  50% {
    left: 50%;
    transform: translateY(0);
  }
  75% {
    left: 75%;
    transform: translateY(-5px);
  }
  100% {
    left: calc(100% + 100px);
    transform: translateY(0);
  }
}

.tracking-dots {
  position: absolute;
  width: 100%;
  height: 100%;
}

.dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: dotPulse 2s ease-in-out infinite;
}

.dot-1 {
  top: 30%;
  left: 20%;
  animation-delay: 0s;
}
.dot-2 {
  top: 60%;
  left: 40%;
  animation-delay: 0.4s;
}
.dot-3 {
  top: 40%;
  left: 60%;
  animation-delay: 0.8s;
}
.dot-4 {
  top: 70%;
  left: 80%;
  animation-delay: 1.2s;
}
.dot-5 {
  top: 50%;
  left: 10%;
  animation-delay: 1.6s;
}

@keyframes dotPulse {
  0%,
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

.hero-content {
  text-align: center;
  color: white;
  z-index: 1;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.title-word {
  display: inline-block;
  opacity: 0;
  transform: translateY(50px) rotateX(90deg);
  animation: wordReveal 1s ease forwards;
}

.title-word:nth-child(1) {
  animation-delay: 0.2s;
}
.title-word:nth-child(2) {
  animation-delay: 0.4s;
}
.title-word:nth-child(3) {
  animation-delay: 0.6s;
}

.highlight {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes wordReveal {
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0;
  animation: fadeInUp 1s ease 1s forwards;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tracking-form {
  max-width: 600px;
  margin: 0 auto;
  opacity: 0;
  transform: translateY(30px);
  animation: formAppear 1s ease 1.5s forwards;
}

@keyframes formAppear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-group {
  display: flex;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 50px;
  padding: 8px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.input-group:focus-within {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: scale(1.02);
}

#tracking-number {
  flex: 1;
  border: none;
  background: transparent;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  outline: none;
  color: #333;
}

#tracking-number::placeholder {
  color: #6b7280;
}

.track-btn {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 150px;
}

.track-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(67, 233, 123, 0.3);
}

.track-btn.loading {
  pointer-events: none;
}

/* Live Map */
.live-map {
  margin: 4rem 0;
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

  .map-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .map-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
  }

  .map-controls {
    display: flex;
    gap: 0.5rem;
  }

  .map-btn {
    background: #f3f4f6;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .map-btn.active,
  .map-btn:hover {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
  }

  .map-container {
    height: 400px;
    position: relative;
    background: #f8fafc;
  }

  .map-placeholder {
    width: 100%;
    height: 100%;
    position: relative;
    background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
    overflow: hidden;
  }

  .delivery-route {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .route-line {
    position: absolute;
    top: 50%;
    left: 10%;
    right: 10%;
    height: 3px;
    background: linear-gradient(90deg, #43e97b 0%, #667eea 50%, #38f9d7 100%);
    border-radius: 2px;
    transform: translateY(-50%);
    animation: routeProgress 3s ease-in-out infinite;
  }

  @keyframes routeProgress {
    0%,
    100% {
      opacity: 0.7;
    }
    50% {
      opacity: 1;
      box-shadow: 0 0 20px rgba(67, 233, 123, 0.5);
    }
  }

  .location-marker {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
  }

  .location-marker.start {
    left: 10%;
  }

  .location-marker.current {
    left: 60%;
  }

  .location-marker.end {
    right: 10%;
  }

  .marker-pulse {
    width: 20px;
    height: 20px;
    background: #43e97b;
    border-radius: 50%;
    margin: 0 auto 0.5rem;
    position: relative;
  }

  .marker-pulse.active {
    background: #667eea;
    animation: markerPulse 2s ease-in-out infinite;
  }

  @keyframes markerPulse {
    0%,
    100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    50% {
      transform: scale(1.2);
      box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
  }

  .marker-label {
    font-size: 0.8rem;
    font-weight: 500;
    color: #1f2937;
    background: white;
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
  }

  /* Package Details */
  .package-details {
    margin: 4rem 0;
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  }

  .package-details h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    color: #1f2937;
  }

  .details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .detail-item {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .detail-label {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  .detail-value {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
  }

  /* Tracking Features */
  .tracking-features {
    padding: 120px 0;
    background: #fafbfc;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .section-header h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .section-header p {
    font-size: 1.2rem;
    color: #6b7280;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .feature-card {
    background: white;
    padding: 2.5rem 2rem;
    border-radius: 16px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
  }

  .feature-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(67, 233, 123, 0.05) 0%,
      rgba(56, 249, 215, 0.05) 100%
    );
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  .feature-card:hover::before {
    opacity: 1;
  }

  .feature-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(67, 233, 123, 0.3);
  }

  .feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    color: white;
    transition: all 0.4s ease;
    position: relative;
    z-index: 1;
  }

  .feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
  }

  .feature-card h4 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
    position: relative;
    z-index: 1;
  }

  .feature-card p {
    color: #6b7280;
    line-height: 1.6;
    position: relative;
    z-index: 1;
  }

  /* Track Another CTA */
  .track-another-cta {
    padding: 120px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .track-another-cta::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="tracking-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23tracking-pattern)"/></svg>');
    z-index: 0;
  }

  .cta-content {
    position: relative;
    z-index: 1;
  }

  .cta-content h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }

  .cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
  }

  .btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  .btn:hover::before {
    left: 100%;
  }

  .btn-primary {
    background: white;
    color: #667eea;
    border-color: white;
  }

  .btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
  }

  .btn-secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-3px);
  }
}

.track-btn.loading {
  pointer-events: none;
}

.btn-text {
  transition: opacity 0.3s ease;
}

.btn-loader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.track-btn.loading .btn-text {
  opacity: 0;
}

.track-btn.loading .btn-loader {
  opacity: 1;
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

.form-help {
  text-align: center;
  font-size: 0.9rem;
  opacity: 0.8;
  margin-top: 0.5rem;
}

/* Tracking Results */
.tracking-results {
  padding: 80px 0;
  background: white;
}

.results-header {
  text-align: center;
  margin-bottom: 4rem;
}

.results-header h2 {
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.package-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  max-width: 800px;
  margin: 0 auto;
}

.info-item {
  text-align: center;
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.info-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.label {
  display: block;
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.value {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.status-badge {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
}

/* Progress Timeline */
.progress-timeline {
  max-width: 800px;
  margin: 4rem auto;
  position: relative;
}

.progress-timeline::before {
  content: "";
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #e5e7eb;
  z-index: 0;
}

.timeline-item {
  display: flex;
  margin-bottom: 3rem;
  position: relative;
  opacity: 0;
  transform: translateX(-50px);
  animation: timelineSlide 0.8s ease forwards;
}

.timeline-item:nth-child(1) {
  animation-delay: 0.2s;
}
.timeline-item:nth-child(2) {
  animation-delay: 0.4s;
}
.timeline-item:nth-child(3) {
  animation-delay: 0.6s;
}
.timeline-item:nth-child(4) {
  animation-delay: 0.8s;
}

@keyframes timelineSlide {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.timeline-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 2rem;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  transition: all 0.4s ease;
}

.timeline-item.completed .timeline-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  color: white;
}

.timeline-item.active .timeline-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.timeline-item.pending .timeline-icon {
  background: #f3f4f6;
  color: #6b7280;
}

.pulse-ring {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px solid #667eea;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

.timeline-content h4 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.timeline-content p {
  color: #6b7280;
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.timestamp {
  font-size: 0.9rem;
  color: #9ca3af;
  font-weight: 500;
}
