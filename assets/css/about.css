/* About Us Page - Modern Animations */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background: #fafbfc;
  color: #333;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.nav-logo a {
  font-size: 2rem;
  font-weight: 800;
  color: #667eea;
  text-decoration: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-menu a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
  width: 100%;
}

.nav-cta {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  padding: 12px 24px;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.nav-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Hero Section */
.hero-about {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -2;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 20s infinite linear;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 15%;
  animation-delay: -5s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  top: 80%;
  left: 20%;
  animation-delay: -10s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  top: 30%;
  right: 30%;
  animation-delay: -15s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

.hero-content {
  text-align: center;
  color: white;
  z-index: 1;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.title-line {
  display: block;
  opacity: 0;
  transform: translateY(50px);
  animation: slideUp 1s ease forwards;
}

.title-line:nth-child(2) {
  animation-delay: 0.3s;
}

.highlight {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0;
  animation: fadeIn 1s ease 0.8s forwards;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 4rem;
}

.stat-item {
  text-align: center;
  opacity: 0;
  transform: translateY(30px);
  animation: statAppear 0.8s ease forwards;
}

.stat-item:nth-child(1) {
  animation-delay: 1.2s;
}
.stat-item:nth-child(2) {
  animation-delay: 1.4s;
}
.stat-item:nth-child(3) {
  animation-delay: 1.6s;
}

@keyframes statAppear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-number {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid white;
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  40% {
    transform: translateX(-50%) translateY(-10px);
  }
  60% {
    transform: translateX(-50%) translateY(-5px);
  }
}

/* Our Story Section */
.our-story {
  padding: 120px 0;
  background: #ffffff;
}

/* Mission & Vision Section */
.mission-vision {
  padding: 120px 0;
  background: #fafbfc;
}

.mv-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 3rem;
}

.mv-card {
  background: white;
  padding: 3rem 2rem;
  border-radius: 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

  .mv-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transition: transform 0.4s ease;
  }

  .mv-card:hover::before {
    transform: scaleX(1);
  }

  .mv-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .mv-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.4s ease;
  }

  .mv-card:hover .mv-icon {
    transform: scale(1.1) rotate(5deg);
  }

  .mv-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1f2937;
  }

  .mv-card p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #6b7280;
  }

  .card-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 200px;
    height: 200px;
    background: radial-gradient(
      circle,
      rgba(102, 126, 234, 0.1) 0%,
      transparent 70%
    );
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  .mv-card:hover .card-glow {
    opacity: 1;
  }

  /* Values Section */
  .values-section {
    padding: 120px 0;
    background: white;
  }

  .section-header {
    text-align: center;
    margin-bottom: 4rem;
  }

  .section-header h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .section-header p {
    font-size: 1.2rem;
    color: #6b7280;
  }

  .values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .value-card {
    background: #fafbfc;
    padding: 2.5rem 2rem;
    border-radius: 16px;
    text-align: center;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid #e5e7eb;
    position: relative;
    overflow: hidden;
  }

  .value-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(102, 126, 234, 0.05) 0%,
      rgba(118, 75, 162, 0.05) 100%
    );
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  .value-card:hover::before {
    opacity: 1;
  }

  .value-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
  }

  .value-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.4s ease;
    position: relative;
    z-index: 1;
  }

  .value-icon.reliability {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .value-icon.innovation {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .value-icon.transparency {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .value-icon.excellence {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .value-card:hover .value-icon {
    transform: scale(1.1) rotate(10deg);
  }

  .value-card h4 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1f2937;
    position: relative;
    z-index: 1;
  }

  .value-card p {
    color: #6b7280;
    line-height: 1.6;
    position: relative;
    z-index: 1;
  }

  /* CTA Section */
  .cta-section {
    padding: 120px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .cta-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    z-index: 0;
  }

  .cta-content {
    position: relative;
    z-index: 1;
  }

  .cta-content h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }

  .cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }

  .btn-primary {
    background: white;
    color: #667eea;
    border-color: white;
  }

  .btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
  }

  .btn-secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-3px);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .hero-title {
      font-size: 2.5rem;
    }

    .hero-stats {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .story-content {
      grid-template-columns: 1fr;
      gap: 3rem;
    }

    .mv-grid {
      grid-template-columns: 1fr;
    }

    .values-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .cta-content h2 {
      font-size: 2rem;
    }

    .cta-buttons {
      flex-direction: column;
      align-items: center;
    }

    .nav-menu {
      display: none;
    }
  }
}

.story-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.story-text h2 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.story-intro {
  font-size: 1.2rem;
  line-height: 1.8;
  margin-bottom: 3rem;
  color: #6b7280;
}

.story-timeline {
  position: relative;
}

.story-timeline::before {
  content: "";
  position: absolute;
  left: 30px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.timeline-item {
  display: flex;
  margin-bottom: 2rem;
  opacity: 0;
  transform: translateX(-30px);
  transition: all 0.6s ease;
}

.timeline-item.animate {
  opacity: 1;
  transform: translateX(0);
}

.timeline-year {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 700;
  margin-right: 2rem;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.timeline-content h4 {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.timeline-content p {
  color: #6b7280;
  line-height: 1.6;
}

/* Visual Container */
.visual-container {
  position: relative;
  height: 400px;
}

.growth-chart {
  display: flex;
  align-items: end;
  justify-content: center;
  height: 200px;
  gap: 1rem;
  margin-bottom: 2rem;
}

.chart-bar {
  width: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px 20px 0 0;
  transition: all 1s ease;
  transform-origin: bottom;
  animation: growBar 1.5s ease forwards;
}

.chart-bar:nth-child(1) {
  animation-delay: 0.2s;
}
.chart-bar:nth-child(2) {
  animation-delay: 0.4s;
}
.chart-bar:nth-child(3) {
  animation-delay: 0.6s;
}
.chart-bar:nth-child(4) {
  animation-delay: 0.8s;
}

@keyframes growBar {
  from {
    height: 0;
    transform: scaleY(0);
  }
  to {
    transform: scaleY(1);
  }
}

.floating-icons {
  position: absolute;
  width: 100%;
  height: 100%;
}

.icon-item {
  position: absolute;
  font-size: 2rem;
  background: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  animation: floatIcon 3s ease-in-out infinite;
}

.icon-1 {
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}

.icon-2 {
  top: 30%;
  right: 10%;
  animation-delay: 0.5s;
}

.icon-3 {
  bottom: 30%;
  left: 10%;
  animation-delay: 1s;
}

.icon-4 {
  bottom: 10%;
  right: 30%;
  animation-delay: 1.5s;
}

@keyframes floatIcon {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}
