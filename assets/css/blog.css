/* Blog Page Styles */

/* Blog Header Background */
.blog-header-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -2;
}

.blog-header-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    z-index: -1;
}

/* Override main site header for blog */
body {
    position: relative;
}

/* Blog Hero Section */
.blog-hero-section {
    background: transparent;
    color: white;
    padding: 8rem 0 4rem;
    text-align: center;
    position: relative;
    z-index: 1;
}

.blog-hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.breadcrumb {
    margin-bottom: 2rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: white;
}

.separator {
    margin: 0 0.5rem;
    opacity: 0.6;
}

.current {
    color: white;
    font-weight: 600;
}

.blog-hero-section h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    font-weight: 700;
    line-height: 1.2;
}

.blog-hero-section p {
    font-size: 1.2rem;
    opacity: 0.9;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

.blog-main {
    background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 2rem 0;
    margin-top: 0;
    position: relative;
    z-index: 1;
}

/* Blog Article Header */
.blog-hero {
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    margin-bottom: 3rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.blog-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
}

.blog-meta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    align-items: center;
}

.category {
    background: #8b5cf6;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.date {
    color: #6b7280;
    font-size: 0.875rem;
}

.blog-hero h1 {
    font-size: 2.5rem;
    color: #1f2937;
    margin-bottom: 1rem;
    font-weight: 700;
    line-height: 1.2;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.blog-subtitle {
    color: #6b7280;
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.author-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.author-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.author-details {
    text-align: left;
}

.author-name {
    display: block;
    font-weight: 600;
    color: #1f2937;
    font-size: 0.9rem;
}

.author-title {
    display: block;
    color: #6b7280;
    font-size: 0.8rem;
}

/* Interactive Buttons */
.blog-interactions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.interaction-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: 2px solid #e5e7eb;
    border-radius: 25px;
    background: white;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.interaction-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #d1d5db;
}

.interaction-btn.liked {
    border-color: #ef4444;
    color: #ef4444;
    background: #fef2f2;
}

.interaction-btn.liked svg {
    fill: #ef4444;
}

.interaction-btn.bookmarked {
    border-color: #f59e0b;
    color: #f59e0b;
    background: #fffbeb;
}

.interaction-btn.bookmarked svg {
    fill: #f59e0b;
}

.share-dropdown {
    position: relative;
}

.share-menu {
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    margin-top: 0.5rem;
}

.share-menu.active {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.share-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    text-decoration: none;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.share-option:hover {
    background: #f3f4f6;
}

.share-option.facebook:hover {
    background: #eff6ff;
    color: #1d4ed8;
}

.share-option.twitter:hover {
    background: #f0f9ff;
    color: #0ea5e9;
}

.share-option.linkedin:hover {
    background: #eff6ff;
    color: #2563eb;
}

.count {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Animated Stats */
.blog-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    display: block;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stat-label {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Trust Badges */
.trust-badges {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Blog Content */
.blog-content {
    max-width: 800px;
    margin: 0 auto;
}

/* Reason Cards */
.reason-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 2rem;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
    transition: all 0.4s ease;
    animation: slideInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.reason-card:nth-child(1) { animation-delay: 0.1s; }
.reason-card:nth-child(2) { animation-delay: 0.2s; }
.reason-card:nth-child(3) { animation-delay: 0.3s; }
.reason-card:nth-child(4) { animation-delay: 0.4s; }
.reason-card:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.reason-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.reason-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
}

.reason-card.purple {
    background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.reason-card.purple::before {
    background: linear-gradient(90deg, #8b5cf6, #a855f7, #c084fc);
}

.reason-card.blue {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.reason-card.blue::before {
    background: linear-gradient(90deg, #3b82f6, #1d4ed8, #60a5fa);
}

.reason-card.green {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.reason-card.green::before {
    background: linear-gradient(90deg, #10b981, #059669, #34d399);
}

.reason-card.orange {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.reason-card.orange::before {
    background: linear-gradient(90deg, #f59e0b, #d97706, #fbbf24);
}

.reason-card.pink {
    background: linear-gradient(135deg, #fdf2f8 0%, #fce7f3 100%);
}

.reason-card.pink::before {
    background: linear-gradient(90deg, #ec4899, #db2777, #f472b6);
}

.reason-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    flex-shrink: 0;
}

.reason-card.purple .reason-number {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.reason-card.blue .reason-number {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.reason-card.green .reason-number {
    background: linear-gradient(135deg, #10b981, #059669);
}

.reason-card.orange .reason-number {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.reason-card.pink .reason-number {
    background: linear-gradient(135deg, #ec4899, #db2777);
}

.reason-content h3 {
    font-size: 1.5rem;
    color: #1f2937;
    margin-bottom: 1rem;
    font-weight: 600;
}

.reason-content p {
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.reason-benefits {
    list-style: none;
    padding: 0;
}

.reason-benefits li {
    color: #374151;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
    font-size: 0.9rem;
}

.reason-benefits li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #10b981;
    font-weight: bold;
}

/* Blog CTA */
.blog-cta {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 3rem 2rem;
    border-radius: 16px;
    text-align: center;
    margin: 3rem 0;
}

.cta-content h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.cta-content p {
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.cta-buttons .btn-primary {
    background: white;
    color: #3b82f6;
}

.cta-buttons .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.cta-buttons .btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.cta-buttons .btn-secondary:hover {
    background: white;
    color: #3b82f6;
}

/* Related Articles */
.related-articles {
    margin-top: 4rem;
}

.related-articles h3 {
    text-align: center;
    font-size: 2rem;
    color: #1f2937;
    margin-bottom: 2rem;
    font-weight: 600;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.article-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
}

.article-image {
    height: 200px;
    overflow: hidden;
}

.placeholder-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
}

.green-bg {
    background: linear-gradient(135deg, #10b981, #059669);
}

.blue-bg {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.purple-bg {
    background: linear-gradient(135deg, #8b5cf6, #a855f7);
}

.article-content {
    padding: 1.5rem;
}

.article-category {
    background: #e5e7eb;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.article-content h4 {
    font-size: 1.2rem;
    color: #1f2937;
    margin: 1rem 0 0.5rem;
    font-weight: 600;
}

.article-content p {
    color: #6b7280;
    line-height: 1.5;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.read-more {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.read-more:hover {
    color: #1d4ed8;
}

/* Enhanced Newsletter Section */
.newsletter-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4rem 2rem;
    border-radius: 20px;
    margin: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.newsletter-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.newsletter-content {
    position: relative;
    z-index: 2;
}

.newsletter-icon {
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
}

.newsletter-section h3 {
    font-size: 2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.newsletter-section p {
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-group-inline {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem;
    border-radius: 50px;
    backdrop-filter: blur(10px);
}

.form-group-inline input {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.9);
    color: #374151;
    font-size: 0.875rem;
}

.form-group-inline input::placeholder {
    color: #9ca3af;
}

.form-group-inline input:focus {
    outline: none;
    background: white;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3);
}

.newsletter-btn {
    padding: 0.75rem 1.5rem;
    background: white;
    color: #667eea;
    border: none;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.newsletter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

.newsletter-benefits {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.benefit {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    opacity: 0.9;
}

.newsletter-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    margin-top: 2rem;
}

.newsletter-stat {
    text-align: center;
}

.newsletter-stat .stat-number {
    font-size: 2rem;
    font-weight: 700;
    display: block;
    color: white;
}

.newsletter-stat .stat-label {
    font-size: 0.875rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Enhanced Animations */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
}

.reason-number {
    animation: bounce 2s infinite;
}

.reason-number:hover {
    animation: pulse 1s infinite;
}

/* Enhanced Blog CTA */
.blog-cta {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-size: 200% 200%;
    animation: gradientShift 8s ease infinite;
    position: relative;
    overflow: hidden;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.blog-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 3s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .blog-hero-section {
        padding: 6rem 0 3rem;
    }

    .blog-hero-section h1 {
        font-size: 2.2rem;
    }

    .blog-hero-section p {
        font-size: 1rem;
    }

    .blog-hero {
        padding: 2rem 1.5rem;
    }

    .blog-hero h1 {
        font-size: 2rem;
    }

    .blog-interactions {
        gap: 0.5rem;
    }

    .interaction-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .blog-stats {
        gap: 1rem;
        padding: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .trust-badges {
        gap: 1rem;
    }

    .badge {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .reason-card {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;
    }

    .reason-number {
        align-self: center;
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .cta-buttons .btn {
        width: 200px;
    }

    .newsletter-benefits {
        flex-direction: column;
        gap: 0.75rem;
    }

    .newsletter-stats {
        gap: 2rem;
    }

    .form-group-inline {
        flex-direction: column;
        gap: 0.75rem;
        border-radius: 16px;
    }

    .form-group-inline input,
    .newsletter-btn {
        border-radius: 12px;
    }
}
