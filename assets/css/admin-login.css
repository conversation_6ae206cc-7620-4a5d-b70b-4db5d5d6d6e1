/* ZamSend Admin Login Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #58b8be 50%, #f093fb 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
}

/* Background Elements */
.login-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Login Container */
.login-container {
    width: 100%;
    max-width: 480px;
    padding: 1.5rem;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    justify-content: center;
}

/* Login Card */
.login-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 2.5rem 2rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: slideUp 0.8s ease;
    width: 100%;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Login Header */
.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.logo i {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo span {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
}

.login-header h1 {
    font-size: 1.8rem;
    color: #1a202c;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.login-header p {
    color: #6b7280;
    font-size: 0.95rem;
}

/* Form Styles */
.login-form {
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.25rem;
    position: relative;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #374151;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.form-group label i {
    color: #6b7280;
    width: 16px;
}

.form-group input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    background: #f9fafb;
    color: #1f2937;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #3b82f6;
    background: #f3f4f6;
}

.input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    transition: width 0.3s ease;
}

.form-group input:focus + .input-border {
    width: 100%;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #374151;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input:checked + .checkmark {
    background: #3b82f6;
    border-color: #3b82f6;
}

.remember-me input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.forgot-password {
    color: #3b82f6;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #1d4ed8;
}

/* Login Button */
.login-btn {
    width: 100%;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.login-btn:hover::before {
    left: 100%;
}

/* Sign Up Option */
.signup-option {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

.signup-option p {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.signup-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: transparent;
    color: #3b82f6;
    border: 2px solid #3b82f6;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.signup-btn:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

/* Social Login Divider */
.login-divider {
    display: flex;
    align-items: center;
    margin: 2rem 0 1.5rem;
    text-align: center;
}

.login-divider::before,
.login-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e5e7eb;
}

.login-divider span {
    padding: 0 1rem;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
    background: white;
}

/* Social Login Buttons */
.social-login {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    color: #374151;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.social-btn svg {
    flex-shrink: 0;
}

/* Google Button */
.google-btn:hover {
    border-color: #4285F4;
    background: #f8faff;
    color: #4285F4;
}

/* GitHub Button */
.github-btn {
    color: #24292e;
}

.github-btn:hover {
    border-color: #24292e;
    background: #f6f8fa;
    color: #24292e;
}

.github-btn svg {
    color: #24292e;
}

/* Facebook Button */
.facebook-btn {
    color: #1877f2;
}

.facebook-btn:hover {
    border-color: #1877f2;
    background: #f0f8ff;
    color: #1877f2;
}

.facebook-btn svg {
    color: #1877f2;
}

/* Social Button Loading State */
.social-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.social-btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}





/* Footer */
.login-footer {
    text-align: center;
    margin-top: 2rem;
    color: rgba(0, 0, 0, 0.8);
    font-size: 0.85rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
}

.footer-links a {
    color: rgba(0, 0, 0, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: rgb(3, 255, 24);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.success-message.active {
    opacity: 1;
    visibility: visible;
}

.success-content {
    background: white;
    padding: 3rem 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: successPop 0.5s ease;
}

@keyframes successPop {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.success-content i {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.success-content h3 {
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.success-content p {
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 1rem 0;
    }

    .login-container {
        padding: 1rem;
        min-height: auto;
        justify-content: flex-start;
    }

    .login-card {
        padding: 2rem 1.5rem;
        max-height: none;
        margin: 1rem 0;
    }

    .login-header {
        margin-bottom: 1.5rem;
    }

    .logo {
        margin-bottom: 1rem;
    }

    .security-features {
        flex-direction: column;
        gap: 1rem;
        margin-top: 1rem;
        padding-top: 1rem;
    }

    .security-item {
        flex-direction: row;
        justify-content: center;
        gap: 0.75rem;
    }

    .social-login {
        flex-direction: column;
        gap: 0.75rem;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    .login-container {
        max-width: 100%;
        padding: 0.75rem;
    }

    .login-card {
        padding: 1.5rem 1rem;
        border-radius: 16px;
    }

    .login-header h1 {
        font-size: 1.5rem;
    }

    .logo span {
        font-size: 1.5rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
        margin-bottom: 1.25rem;
    }


}
