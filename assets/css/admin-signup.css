/* ZamSend Admin Signup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #c55cf6 0%, #03dbd0e7 50%, #6d28d9 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow-x: hidden;
    padding: 2rem 0;
}

/* Background Elements */
.signup-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 10s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: 5%;
    left: 5%;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    top: 40%;
    right: 5%;
    animation-delay: 3s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    bottom: 10%;
    left: 30%;
    animation-delay: 6s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-40px) rotate(180deg); }
}

/* Signup Container */
.signup-container {
    width: 100%;
    max-width: 600px;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

/* Signup Card */
.signup-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 3rem 2.5rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: slideUp 0.8s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header */
.signup-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.logo i {
    font-size: 2.5rem;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo span {
    font-size: 2rem;
    font-weight: 700;
    color: #1a202c;
}

.signup-header h1 {
    font-size: 1.8rem;
    color: #1a202c;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.signup-header p {
    color: #6b7280;
    font-size: 0.95rem;
}

/* Form Styles */
.signup-form {
    margin-bottom: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #374151;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.form-group label i {
    color: #6b7280;
    width: 16px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    background: #f9fafb;
    color: #1f2937;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #8b5cf6;
    background: white;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-group select {
    cursor: pointer;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: #8b5cf6;
    background: #f3f4f6;
}

/* Password Requirements */
.password-requirements {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.password-requirements h4 {
    color: #374151;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.password-requirements ul {
    list-style: none;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.password-requirements li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #6b7280;
    position: relative;
    padding-left: 1.5rem;
}

.password-requirements li::before {
    content: '✗';
    position: absolute;
    left: 0;
    color: #ef4444;
    font-weight: bold;
}

.password-requirements li.valid::before {
    content: '✓';
    color: #10b981;
}

.password-requirements li.valid {
    color: #10b981;
}

/* Terms Section */
.terms-section {
    margin-bottom: 2rem;
}

.terms-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #374151;
    line-height: 1.5;
}

.terms-checkbox input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.terms-checkbox input:checked + .checkmark {
    background: #8b5cf6;
    border-color: #8b5cf6;
}

.terms-checkbox input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.terms-link, .privacy-link {
    color: #8b5cf6;
    text-decoration: none;
    font-weight: 500;
}

.terms-link:hover, .privacy-link:hover {
    color: #7c3aed;
    text-decoration: underline;
}

/* Signup Button */
.signup-btn {
    width: 100%;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    position: relative;
    overflow: hidden;
    margin-bottom: 2rem;
}

.signup-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
}

.signup-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.signup-btn:hover::before {
    left: 100%;
}

/* Social Signup Divider */
.signup-divider {
    display: flex;
    align-items: center;
    margin: 2rem 0 1.5rem;
    text-align: center;
}

.signup-divider::before,
.signup-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: #e5e7eb;
}

.signup-divider span {
    padding: 0 1rem;
    color: #6b7280;
    font-size: 0.875rem;
    font-weight: 500;
    background: white;
}

/* Social Signup Buttons */
.social-signup {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    color: #374151;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.social-btn svg {
    flex-shrink: 0;
}

/* Google Button */
.google-btn:hover {
    border-color: #4285F4;
    background: #f8faff;
    color: #4285F4;
}

/* GitHub Button */
.github-btn {
    color: #24292e;
}

.github-btn:hover {
    border-color: #24292e;
    background: #f6f8fa;
    color: #24292e;
}

.github-btn svg {
    color: #24292e;
}

/* Facebook Button */
.facebook-btn {
    color: #1877f2;
}

.facebook-btn:hover {
    border-color: #1877f2;
    background: #f0f8ff;
    color: #1877f2;
}

.facebook-btn svg {
    color: #1877f2;
}

/* Social Button Loading State */
.social-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.social-btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Login Option */
.login-option {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.login-option p {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.login-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: #8b5cf6;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.login-link:hover {
    color: #7c3aed;
}

/* Approval Notice */
.approval-notice {
    background: #fef3c7;
    border: 1px solid #fbbf24;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.notice-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: #92400e;
    font-weight: 600;
    font-size: 0.9rem;
}

.approval-notice p {
    color: #92400e;
    font-size: 0.85rem;
    line-height: 1.5;
}

/* Security Features */
.security-features {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e5e7eb;
}

.security-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
    flex: 1;
}

.security-item i {
    color: #8b5cf6;
    font-size: 1.2rem;
}

.security-item span {
    font-size: 0.75rem;
    color: #6b7280;
    font-weight: 500;
}

/* Footer */
.signup-footer {
    text-align: center;
    margin-top: 2rem;
    color: rgba(20, 20, 20, 0.8);
    font-size: 0.85rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: rgba(15, 15, 15, 0.9);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: rgb(0, 255, 157);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #8b5cf6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.success-message.active {
    opacity: 1;
    visibility: visible;
}

.success-content {
    background: white;
    padding: 3rem 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: successPop 0.5s ease;
    max-width: 400px;
}

@keyframes successPop {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.success-content i {
    font-size: 4rem;
    color: #10b981;
    margin-bottom: 1rem;
}

.success-content h3 {
    color: #1f2937;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.success-content p {
    color: #6b7280;
    margin-bottom: 2rem;
    line-height: 1.5;
}

.success-btn {
    background: #10b981;
    color: white;
    border: none;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.success-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .signup-container {
        padding: 1rem;
    }

    .signup-card {
        padding: 2rem 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .password-requirements ul {
        grid-template-columns: 1fr;
    }

    .social-signup {
        flex-direction: column;
        gap: 0.75rem;
    }

    .security-features {
        flex-direction: column;
        gap: 1.5rem;
    }

    .security-item {
        flex-direction: row;
        justify-content: center;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    .signup-header h1 {
        font-size: 1.5rem;
    }

    .logo span {
        font-size: 1.5rem;
    }

    .terms-checkbox {
        font-size: 0.85rem;
    }
}
