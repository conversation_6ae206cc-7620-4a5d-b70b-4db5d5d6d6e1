/* FAQ Page - Modern Animations */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background: #fafbfc;
  color: #333;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation - Same as other pages */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.nav-logo a {
  font-size: 2rem;
  font-weight: 800;
  color: #667eea;
  text-decoration: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-menu a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
  width: 100%;
}

.nav-cta {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  padding: 12px 24px;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.nav-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Hero Section */
.hero-faq {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  z-index: -2;
}

.floating-questions {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.question-mark {
  position: absolute;
  font-size: 4rem;
  font-weight: 800;
  color: rgba(255, 255, 255, 0.1);
  animation: floatQuestion 8s ease-in-out infinite;
}

.q1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.q2 {
  top: 20%;
  right: 15%;
  animation-delay: 1s;
}

.q3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.q4 {
  bottom: 20%;
  right: 25%;
  animation-delay: 3s;
}

.q5 {
  top: 50%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes floatQuestion {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.1;
  }
  25% {
    transform: translateY(-20px) rotate(5deg);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-10px) rotate(-5deg);
    opacity: 0.15;
  }
  75% {
    transform: translateY(-30px) rotate(3deg);
    opacity: 0.25;
  }
}

.hero-content {
  text-align: center;
  color: white;
  z-index: 1;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.title-line {
  display: block;
  opacity: 0;
  transform: translateY(50px) rotateX(90deg);
  animation: titleReveal 1s ease forwards;
}

.title-line:nth-child(1) {
  animation-delay: 0.2s;
}
.title-line:nth-child(2) {
  animation-delay: 0.4s;
}
.title-line:nth-child(3) {
  animation-delay: 0.6s;
}

.highlight {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes titleReveal {
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0;
  animation: fadeInUp 1s ease 1s forwards;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-box {
  max-width: 500px;
  margin: 0 auto;
  position: relative;
  opacity: 0;
  transform: translateY(30px);
  animation: searchAppear 1s ease 1.5s forwards;
}

@keyframes searchAppear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.search-box input {
  width: 100%;
  padding: 1rem 4rem 1rem 1.5rem;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.search-box input:focus {
  outline: none;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: scale(1.02);
}

.search-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border: none;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn:hover {
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}

/* FAQ Categories */
.faq-categories {
  padding: 60px 0;
  background: white;
  border-bottom: 1px solid #e5e7eb;
}

.category-tabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.category-btn {
  background: #f3f4f6;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* Questions CTA */
.questions-cta {
  padding: 120px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  position: relative;
  overflow: hidden;
}

  .questions-cta::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="circles" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23circles)"/></svg>');
    z-index: 0;
  }

  .cta-content {
    position: relative;
    z-index: 1;
  }

  .cta-content h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }

  .cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
  }

  .btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  .btn:hover::before {
    left: 100%;
  }

  .btn-primary {
    background: white;
    color: #667eea;
    border-color: white;
  }

  .btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
  }

  .btn-secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-3px);
  }

  /* Search Functionality Styles */
  .faq-item.hidden {
    display: none;
  }

  .faq-item.highlight {
    border: 2px solid #4facfe;
    box-shadow: 0 0 20px rgba(79, 172, 254, 0.3);
  }

  .no-results {
    text-align: center;
    padding: 4rem 2rem;
    color: #6b7280;
  }

  .no-results h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #1f2937;
  }

  .no-results p {
    font-size: 1.1rem;
    line-height: 1.6;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .hero-title {
      font-size: 2.5rem;
    }

    .search-box {
      max-width: 90%;
    }

    .category-tabs {
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
    }

    .category-btn {
      width: 200px;
      text-align: center;
    }

    .faq-question {
      padding: 1.5rem;
    }

    .faq-question h3 {
      font-size: 1.1rem;
    }

    .cta-content h2 {
      font-size: 2rem;
    }

    .cta-buttons {
      flex-direction: column;
      align-items: center;
    }

    .nav-menu {
      display: none;
    }
  }

  @media (max-width: 480px) {
    .hero-title {
      font-size: 2rem;
    }

    .question-mark {
      font-size: 2rem;
    }

    .search-box input {
      padding: 0.8rem 3.5rem 0.8rem 1rem;
      font-size: 1rem;
    }

    .search-btn {
      width: 35px;
      height: 35px;
      font-size: 1rem;
    }

    .faq-question {
      padding: 1rem;
    }

    .faq-question h3 {
      font-size: 1rem;
    }

    .faq-icon {
      width: 30px;
      height: 30px;
    }

    .cta-content h2 {
      font-size: 1.8rem;
    }
  }

  /* Animation for FAQ items when they appear */
  @keyframes slideInFromLeft {
    from {
      opacity: 0;
      transform: translateX(-50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInFromRight {
    from {
      opacity: 0;
      transform: translateX(50px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .faq-item:nth-child(odd) {
    animation: slideInFromLeft 0.6s ease forwards;
  }

  .faq-item:nth-child(even) {
    animation: slideInFromRight 0.6s ease forwards;
  }

  /* Pulse animation for search button */
  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(79, 172, 254, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(79, 172, 254, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(79, 172, 254, 0);
    }
  }

  .search-btn:focus {
    animation: pulse 1.5s infinite;
  }

  /* Smooth transitions for category switching */
  .faq-category {
    transition: all 0.4s ease;
  }

  .faq-category:not(.active) {
    opacity: 0;
    transform: translateY(20px);
  }

  .faq-category.active {
    opacity: 1;
    transform: translateY(0);
  }
}

.category-btn {
  background: #f3f4f6;
  border: none;
  padding: 1rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.category-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  transition: left 0.4s ease;
  z-index: -1;
}

.category-btn:hover::before,
.category-btn.active::before {
  left: 0;
}

.category-btn:hover,
.category-btn.active {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
}

/* FAQ Content */
.faq-content {
  padding: 80px 0;
  background: #fafbfc;
}

.faq-category {
  display: none;
  max-width: 800px;
  margin: 0 auto;
}

.faq-category.active {
  display: block;
}

.faq-item {
  background: white;
  border-radius: 16px;
  margin-bottom: 1.5rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(30px);
  animation: faqAppear 0.6s ease forwards;
}

.faq-item:nth-child(1) {
  animation-delay: 0.1s;
}
.faq-item:nth-child(2) {
  animation-delay: 0.2s;
}
.faq-item:nth-child(3) {
  animation-delay: 0.3s;
}
.faq-item:nth-child(4) {
  animation-delay: 0.4s;
}

@keyframes faqAppear {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.faq-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
}

.faq-question {
  padding: 2rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.faq-question:hover {
  background: #f8fafc;
}

.faq-question h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.faq-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: all 0.4s ease;
  flex-shrink: 0;
}

.faq-item.active .faq-icon {
  transform: rotate(180deg);
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: #f8fafc;
}

.faq-item.active .faq-answer {
  max-height: 200px;
  padding: 0 2rem 2rem;
}

.faq-answer p {
  color: #6b7280;
  line-height: 1.7;
  margin: 0;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease 0.1s;
}

.faq-item.active .faq-answer p {
  opacity: 1;
  transform: translateY(0);
}
