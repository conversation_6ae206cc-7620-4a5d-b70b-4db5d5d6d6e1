/* Team Page - Modern Animations */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background: #fafbfc;
  color: #333;
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation - Same as About page */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.nav-logo a {
  font-size: 2rem;
  font-weight: 800;
  color: #667eea;
  text-decoration: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-menu a::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.nav-menu a.active::after {
  width: 100%;
}

.nav-cta {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  padding: 12px 24px;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.nav-cta:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* Hero Section */
.hero-team {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  z-index: -2;
}

.animated-grid {
  position: absolute;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 2px;
  opacity: 0.1;
}

.grid-item {
  background: rgba(255, 255, 255, 0.2);
  animation: gridPulse 4s ease-in-out infinite;
}

.grid-item:nth-child(1) {
  animation-delay: 0s;
}
.grid-item:nth-child(2) {
  animation-delay: 0.5s;
}
.grid-item:nth-child(3) {
  animation-delay: 1s;
}
.grid-item:nth-child(4) {
  animation-delay: 1.5s;
}
.grid-item:nth-child(5) {
  animation-delay: 2s;
}
.grid-item:nth-child(6) {
  animation-delay: 2.5s;
}

@keyframes gridPulse {
  0%,
  100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}

.hero-content {
  text-align: center;
  color: white;
  z-index: 1;
}

.hero-title {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.title-word {
  display: inline-block;
  opacity: 0;
  transform: translateY(50px) rotateX(90deg);
  animation: wordReveal 1s ease forwards;
}

.title-word:nth-child(1) {
  animation-delay: 0.2s;
}
.title-word:nth-child(2) {
  animation-delay: 0.4s;
}
.title-word:nth-child(3) {
  animation-delay: 0.6s;
}

.highlight {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes wordReveal {
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

.hero-subtitle {
  font-size: 1.3rem;
  margin-bottom: 3rem;
  opacity: 0;
  animation: fadeInUp 1s ease 1s forwards;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.team-stats {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
  margin-top: 4rem;
}

.stat-bubble {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  padding: 2rem;
  border-radius: 50%;
  width: 150px;
  height: 150px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s ease;
  opacity: 0;
  transform: scale(0);
  animation: bubbleAppear 0.8s ease forwards;
}

.stat-bubble:nth-child(1) {
  animation-delay: 1.4s;
}
.stat-bubble:nth-child(2) {
  animation-delay: 1.6s;
}
.stat-bubble:nth-child(3) {
  animation-delay: 1.8s;
}

@keyframes bubbleAppear {
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.stat-bubble:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 0.25);
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 0.9rem;
  text-align: center;
  opacity: 0.9;
}

/* Leadership Section */
.leadership-section {
  padding: 120px 0;
  background: #ffffff;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-header p {
  font-size: 1.2rem;
  color: #6b7280;
}

.leadership-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
}

.leader-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: rotateY(90deg);
  animation: flipIn 0.8s ease forwards;
}

.leader-card:nth-child(1) { animation-delay: 0.2s; }
.leader-card:nth-child(2) { animation-delay: 0.4s; }
.leader-card:nth-child(3) { animation-delay: 0.6s; }

@keyframes flipIn {
  to {
    opacity: 1;
    transform: rotateY(0);
  }
}

/* Departments Section */
.departments-section {
  padding: 120px 0;
  background: #fafbfc;
}

  .department-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 4rem;
    flex-wrap: wrap;
  }

  .tab-btn {
    background: white;
    border: 2px solid #e5e7eb;
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    color: #6b7280;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .tab-btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    transition: left 0.3s ease;
    z-index: -1;
  }

  .tab-btn:hover::before,
  .tab-btn.active::before {
    left: 0;
  }

  .tab-btn:hover,
  .tab-btn.active {
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
  }

  .department-content {
    position: relative;
    min-height: 400px;
  }

  .tab-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s ease;
    pointer-events: none;
  }

  .tab-content.active {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
  }

  .team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .team-member {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: translateY(30px);
    animation: slideUp 0.6s ease forwards;
  }

  .team-member:nth-child(1) {
    animation-delay: 0.1s;
  }
  .team-member:nth-child(2) {
    animation-delay: 0.2s;
  }
  .team-member:nth-child(3) {
    animation-delay: 0.3s;
  }
  .team-member:nth-child(4) {
    animation-delay: 0.4s;
  }

  @keyframes slideUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .team-member:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  }

  .member-avatar {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto 1.5rem;
  }

  .member-avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    transition: all 0.4s ease;
  }

  .avatar-ring {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border: 3px solid transparent;
    border-radius: 50%;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    background-clip: padding-box;
    opacity: 0;
    transition: all 0.4s ease;
    animation: ringRotate 3s linear infinite;
  }

  @keyframes ringRotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .team-member:hover .avatar-ring {
    opacity: 1;
  }

  .team-member:hover .member-avatar img {
    transform: scale(1.1);
  }

  .team-member h4 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #1f2937;
  }

  .member-role {
    color: #f093fb;
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .member-skills {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
  }

  .member-skills span {
    background: #f3f4f6;
    color: #6b7280;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .team-member:hover .member-skills span {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    transform: translateY(-2px);
  }

  /* Join Team CTA */
  .join-team-cta {
    padding: 120px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .join-team-cta::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagons" width="28" height="49" patternUnits="userSpaceOnUse"><polygon points="14,1 26,8 26,22 14,29 2,22 2,8" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagons)"/></svg>');
    z-index: 0;
  }

  .cta-content {
    position: relative;
    z-index: 1;
  }

  .cta-content h2 {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }

  .cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .btn {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
  }

  .btn::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }

  .btn:hover::before {
    left: 100%;
  }

  .btn-primary {
    background: white;
    color: #667eea;
    border-color: white;
  }

  .btn-primary:hover {
    background: transparent;
    color: white;
    border-color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
  }

  .btn-secondary {
    background: transparent;
    color: white;
    border-color: rgba(255, 255, 255, 0.5);
  }

  .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-3px);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .hero-title {
      font-size: 2.5rem;
    }

    .team-stats {
      flex-direction: column;
      align-items: center;
      gap: 2rem;
    }

    .leadership-grid {
      grid-template-columns: 1fr;
    }

    .department-tabs {
      flex-direction: column;
      align-items: center;
    }

    .team-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .cta-content h2 {
      font-size: 2rem;
    }

    .cta-buttons {
      flex-direction: column;
      align-items: center;
    }

    .nav-menu {
      display: none;
    }
  }

  @media (max-width: 480px) {
    .hero-title {
      font-size: 2rem;
    }

    .stat-bubble {
      width: 120px;
      height: 120px;
      padding: 1.5rem;
    }

    .stat-number {
      font-size: 1.5rem;
    }

    .section-header h2 {
      font-size: 2rem;
    }
  }
  transform: rotateY(90deg);
  animation: flipIn 0.8s ease forwards;
}

.leader-card:nth-child(1) {
  animation-delay: 0.2s;
}
.leader-card:nth-child(2) {
  animation-delay: 0.4s;
}
.leader-card:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes flipIn {
  to {
    opacity: 1;
    transform: rotateY(0);
  }
}

.leader-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.leader-image {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.leader-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
}

.leader-card:hover .leader-image img {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(240, 147, 251, 0.8) 0%,
    rgba(245, 87, 108, 0.8) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s ease;
}

.leader-card:hover .image-overlay {
  opacity: 1;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  font-size: 1.2rem;
  transition: all 0.3s ease;
  transform: translateY(20px);
}

.leader-card:hover .social-link {
  transform: translateY(0);
}

.social-link:nth-child(1) {
  transition-delay: 0.1s;
}
.social-link:nth-child(2) {
  transition-delay: 0.2s;
}
.social-link:nth-child(3) {
  transition-delay: 0.3s;
}

.social-link:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.leader-info {
  padding: 2rem;
}

.leader-info h3 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.leader-role {
  color: #f093fb;
  font-weight: 600;
  margin-bottom: 1rem;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
}

.leader-bio {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.leader-skills {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.skill-tag {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}
