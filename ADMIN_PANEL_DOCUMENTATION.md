# ZamSend Admin Panel - Complete Documentation

## 🎯 Overview

The ZamSend Admin Panel is a comprehensive, production-ready administrative interface for managing courier and logistics operations. Built with modern web technologies, it provides a complete solution for shipment management, user administration, financial tracking, and business analytics.

## 📋 Table of Contents

1. [Features](#features)
2. [Database Schema](#database-schema)
3. [File Structure](#file-structure)
4. [Installation](#installation)
5. [Configuration](#configuration)
6. [API Endpoints](#api-endpoints)
7. [Security Features](#security-features)
8. [User Roles & Permissions](#user-roles--permissions)
9. [Usage Guide](#usage-guide)
10. [Customization](#customization)

## ✨ Features

### Core Functionality
- **Dashboard Analytics** - Real-time KPIs, charts, and performance metrics
- **Shipment Management** - Complete CRUD operations with tracking and status updates
- **User & Staff Management** - Role-based access control with 2FA support
- **Branch Management** - Multi-location support with geolocation
- **Financial Management** - Transaction tracking, payment processing, and reporting
- **Support System** - Ticket management with automated workflows
- **Content Management** - Pages, blog posts, FAQ, and email templates
- **System Settings** - Comprehensive configuration options

### Advanced Features
- **Real-time Updates** - Live notifications and status changes
- **Audit Logging** - Complete activity tracking for compliance
- **Multi-language Support** - Internationalization ready
- **API Integration** - RESTful API with authentication
- **Responsive Design** - Mobile-first approach
- **Performance Optimized** - Fast loading and smooth interactions

## 🗄️ Database Schema

### Core Tables
- **users** - Staff and admin accounts with authentication
- **roles** - RBAC system with JSON permissions
- **branches** - Multi-location support with geolocation
- **shipments** - Complete shipment lifecycle management
- **shipment_events** - Detailed tracking timeline
- **transactions** - Financial records and payment processing
- **support_tickets** - Customer support system
- **audit_logs** - Complete activity tracking

### Content Management
- **pages** - CMS for static pages
- **blog_posts** - Blog management system
- **blog_categories** - Hierarchical categorization
- **faqs** - Frequently asked questions
- **email_templates** - Transactional email management

### System Configuration
- **settings** - Key-value configuration store
- **languages** - Multi-language support
- **translations** - Localization strings
- **api_keys** - API access management
- **webhooks** - External integrations

## 📁 File Structure

```
admin/
├── index.html                 # Main dashboard
├── assets/
│   ├── css/
│   │   ├── admin.css         # Core admin styles
│   │   └── dashboard.css     # Dashboard-specific styles
│   ├── js/
│   │   ├── admin.js          # Core admin functionality
│   │   └── dashboard.js      # Dashboard interactions
│   └── images/
│       └── avatars/          # User profile images
├── shipments/
│   ├── list.html            # Shipment listing
│   ├── create.html          # Create new shipment
│   ├── tracking.html        # Shipment tracking
│   └── bulk-import.html     # Bulk operations
├── users/
│   ├── list.html            # User management
│   ├── create.html          # Add new user
│   ├── roles.html           # Role management
│   └── activity.html        # User activity logs
├── branches/
│   ├── list.html            # Branch listing
│   ├── create.html          # Add new branch
│   ├── map.html             # Branch map view
│   └── performance.html     # Branch analytics
├── payments/
│   ├── transactions.html    # Transaction history
│   ├── methods.html         # Payment methods
│   ├── reconciliation.html  # Cash reconciliation
│   └── reports.html         # Financial reports
├── support/
│   ├── tickets.html         # Support tickets
│   ├── chat.html            # Live chat
│   ├── knowledge-base.html  # Knowledge base
│   └── canned-responses.html # Quick responses
├── reports/
│   ├── dashboard.html       # Report dashboard
│   ├── shipments.html       # Shipment reports
│   ├── financial.html       # Financial reports
│   └── performance.html     # Performance metrics
├── content/
│   ├── pages.html           # Page management
│   ├── blog.html            # Blog management
│   ├── faq.html             # FAQ management
│   └── templates.html       # Email templates
└── settings/
    ├── general.html         # General settings
    ├── email.html           # Email configuration
    ├── sms.html             # SMS settings
    ├── api.html             # API management
    └── security.html        # Security settings

database/
├── zamsend_schema.sql       # Complete database schema
└── sample_data.sql          # Sample data for testing
```

## 🚀 Installation

### Prerequisites
- MySQL 8.0+
- PHP 8.0+ (for backend API)
- Web server (Apache/Nginx)
- Modern web browser

### Database Setup
1. Import the database schema:
```sql
mysql -u root -p < database/zamsend_schema.sql
```

2. Import sample data (optional):
```sql
mysql -u root -p zamsend_admin < database/sample_data.sql
```

### Web Server Configuration
1. Copy admin panel files to your web server directory
2. Configure virtual host to point to the admin directory
3. Ensure proper file permissions for uploads and logs

### Initial Configuration
1. Access the admin panel at `http://yourdomain.com/admin/`
2. Default login credentials:
   - Email: `<EMAIL>`
   - Password: `password` (change immediately)

## ⚙️ Configuration

### Environment Variables
Create a `.env` file with the following configuration:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=zamsend_admin
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Application Settings
APP_NAME="ZamSend Admin Panel"
APP_URL=http://localhost
APP_TIMEZONE=Asia/Dubai
APP_CURRENCY=AED

# Security Settings
JWT_SECRET=your_jwt_secret_key
SESSION_LIFETIME=3600
BCRYPT_ROUNDS=12

# Email Configuration
MAIL_DRIVER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_password
MAIL_ENCRYPTION=tls

# SMS Configuration
SMS_PROVIDER=twilio
SMS_API_KEY=your_sms_api_key
SMS_FROM_NUMBER=+**********

# Payment Gateways
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_secret
```

## 🔐 Security Features

### Authentication & Authorization
- **JWT Token Authentication** - Secure API access
- **Two-Factor Authentication** - TOTP and SMS backup
- **Role-Based Access Control** - Granular permissions
- **Session Management** - Secure session handling
- **Password Policies** - Strong password requirements

### Security Measures
- **CSRF Protection** - Cross-site request forgery prevention
- **XSS Protection** - Input sanitization and output encoding
- **SQL Injection Prevention** - Parameterized queries
- **Rate Limiting** - API and login attempt throttling
- **IP Whitelisting** - Admin access restrictions
- **Audit Logging** - Complete activity tracking

### Data Protection
- **Encryption at Rest** - Sensitive data encryption
- **HTTPS Enforcement** - Secure data transmission
- **Data Backup** - Automated backup procedures
- **GDPR Compliance** - Data privacy features

## 👥 User Roles & Permissions

### Role Hierarchy
1. **Super Admin** - Full system access
2. **Admin** - Branch and operations management
3. **Branch Manager** - Branch-specific operations
4. **Staff** - Daily operations and shipments
5. **Support** - Customer service and tickets

### Permission Matrix
| Feature | Super Admin | Admin | Branch Manager | Staff | Support |
|---------|-------------|-------|----------------|-------|---------|
| System Settings | ✅ | ❌ | ❌ | ❌ | ❌ |
| User Management | ✅ | ✅ | ❌ | ❌ | ❌ |
| Branch Management | ✅ | ✅ | ✅ | ❌ | ❌ |
| Shipment CRUD | ✅ | ✅ | ✅ | ✅ | ❌ |
| Financial Reports | ✅ | ✅ | ✅ | ❌ | ❌ |
| Support Tickets | ✅ | ✅ | ✅ | ❌ | ✅ |
| Content Management | ✅ | ✅ | ❌ | ❌ | ❌ |

## 📖 Usage Guide

### Dashboard Overview
The main dashboard provides:
- **Key Performance Indicators** - Total shipments, revenue, delivery rates
- **Real-time Charts** - Revenue trends and status distribution
- **Recent Activity** - Latest shipments and notifications
- **Quick Actions** - Common tasks and shortcuts
- **System Alerts** - Important notifications and warnings

### Shipment Management
1. **Create Shipment** - Enter sender/recipient details, select service type
2. **Track Progress** - Monitor shipment status and location updates
3. **Update Status** - Change shipment status and add notes
4. **Generate Reports** - Export shipment data and analytics

### User Administration
1. **Add Users** - Create staff accounts with appropriate roles
2. **Manage Permissions** - Assign roles and customize access levels
3. **Monitor Activity** - Track user actions and login history
4. **Security Settings** - Configure 2FA and password policies

### Financial Management
1. **Transaction Tracking** - Monitor all payments and refunds
2. **Cash Reconciliation** - Balance cash collections at branch level
3. **Payment Methods** - Configure payment gateways and options
4. **Financial Reports** - Generate revenue and expense reports

## 🎨 Customization

### Theming
The admin panel uses CSS custom properties for easy theming:

```css
:root {
    --primary-color: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}
```

### Adding New Features
1. Create HTML templates following the existing structure
2. Add corresponding CSS styles in the appropriate files
3. Implement JavaScript functionality using the established patterns
4. Update navigation menus and permissions as needed

### API Integration
The admin panel is designed to work with a RESTful API. Key endpoints include:

```javascript
// Authentication
POST /api/v1/auth/login
POST /api/v1/auth/logout
POST /api/v1/auth/refresh

// Shipments
GET /api/v1/admin/shipments
POST /api/v1/admin/shipments
PUT /api/v1/admin/shipments/{id}
DELETE /api/v1/admin/shipments/{id}

// Users
GET /api/v1/admin/users
POST /api/v1/admin/users
PUT /api/v1/admin/users/{id}

// Reports
GET /api/v1/admin/reports/dashboard
GET /api/v1/admin/reports/shipments
GET /api/v1/admin/reports/financial
```

## 🔧 Maintenance

### Regular Tasks
- **Database Backups** - Daily automated backups
- **Log Rotation** - Weekly log file rotation
- **Security Updates** - Monthly security patches
- **Performance Monitoring** - Continuous performance tracking

### Troubleshooting
- Check browser console for JavaScript errors
- Verify database connections and permissions
- Review server logs for PHP/Apache errors
- Test API endpoints with proper authentication

## 📞 Support

For technical support and questions:
- **Documentation**: Refer to this comprehensive guide
- **Issue Tracking**: Use the built-in support ticket system
- **Community**: Join the ZamSend developer community
- **Professional Support**: Contact ZamSend technical team

---

**Version**: 1.0.0  
**Last Updated**: December 2024  
**License**: Proprietary - ZamSend Technologies
