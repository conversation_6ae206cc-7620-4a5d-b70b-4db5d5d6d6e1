-- ZamSend Admin Panel Sample Data
-- Insert initial data for development and testing

USE zamsend_admin;

-- =============================================
-- ROLES & PERMISSIONS
-- =============================================

INSERT INTO roles (name, display_name, description, permissions, is_system) VALUES
('super_admin', 'Super Administrator', 'Full system access with all permissions', 
 '{"users": ["create", "read", "update", "delete"], "branches": ["create", "read", "update", "delete"], "shipments": ["create", "read", "update", "delete"], "transactions": ["create", "read", "update", "delete"], "settings": ["create", "read", "update", "delete"], "reports": ["read"], "audit": ["read"]}', 
 TRUE),
('admin', 'Administrator', 'Branch and operations management', 
 '{"users": ["create", "read", "update"], "branches": ["read", "update"], "shipments": ["create", "read", "update", "delete"], "transactions": ["create", "read", "update"], "settings": ["read", "update"], "reports": ["read"]}', 
 TRUE),
('branch_manager', 'Branch Manager', 'Manages specific branch operations', 
 '{"users": ["read"], "branches": ["read"], "shipments": ["create", "read", "update"], "transactions": ["read"], "reports": ["read"]}', 
 TRUE),
('staff', 'Staff Member', 'Daily operations and courier management', 
 '{"shipments": ["create", "read", "update"], "customers": ["create", "read", "update"]}', 
 TRUE),
('support', 'Customer Support', 'Handle customer inquiries and tickets', 
 '{"tickets": ["create", "read", "update"], "shipments": ["read"], "customers": ["read"]}', 
 TRUE);

-- =============================================
-- BRANCHES
-- =============================================

INSERT INTO branches (name, code, address, city, state, country, postal_code, latitude, longitude, timezone, phone, email, working_hours, is_active) VALUES
('Dubai Main Hub', 'DXB001', 'Sheikh Zayed Road, Business Bay', 'Dubai', 'Dubai', 'UAE', '00000', 25.2048, 55.2708, 'Asia/Dubai', '+971-4-123-4567', '<EMAIL>', 
 '{"monday": {"open": "08:00", "close": "18:00"}, "tuesday": {"open": "08:00", "close": "18:00"}, "wednesday": {"open": "08:00", "close": "18:00"}, "thursday": {"open": "08:00", "close": "18:00"}, "friday": {"open": "08:00", "close": "18:00"}, "saturday": {"open": "09:00", "close": "15:00"}, "sunday": {"closed": true}}', 
 TRUE),
('Abu Dhabi Branch', 'AUH001', 'Corniche Road, Al Markaziyah', 'Abu Dhabi', 'Abu Dhabi', 'UAE', '00000', 24.4539, 54.3773, 'Asia/Dubai', '+971-2-123-4567', '<EMAIL>', 
 '{"monday": {"open": "08:00", "close": "18:00"}, "tuesday": {"open": "08:00", "close": "18:00"}, "wednesday": {"open": "08:00", "close": "18:00"}, "thursday": {"open": "08:00", "close": "18:00"}, "friday": {"open": "08:00", "close": "18:00"}, "saturday": {"open": "09:00", "close": "15:00"}, "sunday": {"closed": true}}', 
 TRUE),
('Sharjah Branch', 'SHJ001', 'Al Wahda Street, Al Majaz', 'Sharjah', 'Sharjah', 'UAE', '00000', 25.3463, 55.4209, 'Asia/Dubai', '+971-6-123-4567', '<EMAIL>', 
 '{"monday": {"open": "08:00", "close": "18:00"}, "tuesday": {"open": "08:00", "close": "18:00"}, "wednesday": {"open": "08:00", "close": "18:00"}, "thursday": {"open": "08:00", "close": "18:00"}, "friday": {"open": "08:00", "close": "18:00"}, "saturday": {"open": "09:00", "close": "15:00"}, "sunday": {"closed": true}}', 
 TRUE);

-- =============================================
-- USERS
-- =============================================

INSERT INTO users (uuid, username, email, password, first_name, last_name, phone, role_id, branch_id, status, two_factor_enabled) VALUES
(UUID(), 'superadmin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Super', 'Admin', '+971-50-123-4567', 1, NULL, 'active', FALSE),
(UUID(), 'dubai_admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Ahmed', 'Al-Rashid', '+971-50-234-5678', 2, 1, 'active', TRUE),
(UUID(), 'abudhabi_manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Fatima', 'Al-Zahra', '+971-50-345-6789', 3, 2, 'active', FALSE),
(UUID(), 'dubai_staff1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mohammed', 'Hassan', '+971-50-456-7890', 4, 1, 'active', FALSE),
(UUID(), 'support_agent', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Johnson', '+971-50-567-8901', 5, 1, 'active', FALSE);

-- Update branch managers
UPDATE branches SET manager_id = 2 WHERE id = 1; -- Dubai Main Hub
UPDATE branches SET manager_id = 3 WHERE id = 2; -- Abu Dhabi Branch

-- =============================================
-- COURIER TYPES & UNITS
-- =============================================

INSERT INTO units (name, symbol, type, conversion_factor, is_default) VALUES
('Kilogram', 'kg', 'weight', 1.000000, TRUE),
('Gram', 'g', 'weight', 0.001000, FALSE),
('Pound', 'lb', 'weight', 0.453592, FALSE),
('Centimeter', 'cm', 'dimension', 1.000000, TRUE),
('Meter', 'm', 'dimension', 100.000000, FALSE),
('Inch', 'in', 'dimension', 2.540000, FALSE);

INSERT INTO courier_types (name, description, base_price, price_per_km, price_per_kg, max_weight, max_dimensions, delivery_time_hours, is_active) VALUES
('Standard Delivery', 'Regular delivery service within 2-3 business days', 25.00, 0.50, 2.00, 30.00, '100x100x100', 72, TRUE),
('Express Delivery', 'Fast delivery service within 24 hours', 50.00, 1.00, 3.00, 25.00, '80x80x80', 24, TRUE),
('Same Day Delivery', 'Urgent delivery within the same day', 100.00, 2.00, 5.00, 15.00, '60x60x60', 8, TRUE),
('International Express', 'International delivery service', 150.00, 3.00, 8.00, 50.00, '120x120x120', 168, TRUE);

-- =============================================
-- PAYMENT METHODS
-- =============================================

INSERT INTO payment_methods (name, type, gateway, configuration, is_active) VALUES
('Cash on Delivery', 'cash', NULL, '{}', TRUE),
('Credit/Debit Card', 'card', 'stripe', '{"public_key": "pk_test_...", "secret_key": "sk_test_..."}', TRUE),
('Bank Transfer', 'bank_transfer', NULL, '{"account_number": "*********0", "routing_number": "*********"}', TRUE),
('PayPal', 'digital_wallet', 'paypal', '{"client_id": "client_id", "client_secret": "client_secret"}', TRUE);

-- =============================================
-- CUSTOMERS
-- =============================================

INSERT INTO customers (uuid, first_name, last_name, email, phone, address, city, state, country, postal_code, is_verified) VALUES
(UUID(), 'John', 'Smith', '<EMAIL>', '+971-50-111-2222', 'Marina Walk, Dubai Marina', 'Dubai', 'Dubai', 'UAE', '00000', TRUE),
(UUID(), 'Maria', 'Garcia', '<EMAIL>', '+971-50-333-4444', 'Al Reem Island, Shams Abu Dhabi', 'Abu Dhabi', 'Abu Dhabi', 'UAE', '00000', TRUE),
(UUID(), 'Ahmed', 'Al-Mahmoud', '<EMAIL>', '+971-50-555-6666', 'Al Majaz Waterfront, Sharjah', 'Sharjah', 'Sharjah', 'UAE', '00000', FALSE);

-- =============================================
-- SAMPLE SHIPMENTS
-- =============================================

INSERT INTO shipments (
    tracking_number, reference_number, sender_id, sender_name, sender_phone, sender_email, sender_address, sender_city, sender_country,
    recipient_name, recipient_phone, recipient_email, recipient_address, recipient_city, recipient_country,
    origin_branch_id, destination_branch_id, courier_type_id, weight, weight_unit_id, declared_value, currency,
    base_cost, total_cost, payment_status, status, assigned_staff_id, priority, created_by
) VALUES
('ZS2024001001', 'REF001', 1, 'John Smith', '+971-50-111-2222', '<EMAIL>', 'Marina Walk, Dubai Marina', 'Dubai', 'UAE',
 'Maria Garcia', '+971-50-333-4444', '<EMAIL>', 'Al Reem Island, Shams Abu Dhabi', 'Abu Dhabi', 'UAE',
 1, 2, 2, 2.500, 1, 500.00, 'AED', 50.00, 57.50, 'paid', 'in_transit', 4, 'normal', 2),

('ZS2024001002', 'REF002', 2, 'Maria Garcia', '+971-50-333-4444', '<EMAIL>', 'Al Reem Island, Shams Abu Dhabi', 'Abu Dhabi', 'UAE',
 'Ahmed Al-Mahmoud', '+971-50-555-6666', '<EMAIL>', 'Al Majaz Waterfront, Sharjah', 'Sharjah', 'UAE',
 2, 3, 1, 1.200, 1, 200.00, 'AED', 25.00, 27.40, 'pending', 'pending', NULL, 'normal', 3),

('ZS2024001003', 'REF003', 3, 'Ahmed Al-Mahmoud', '+971-50-555-6666', '<EMAIL>', 'Al Majaz Waterfront, Sharjah', 'Sharjah', 'UAE',
 'John Smith', '+971-50-111-2222', '<EMAIL>', 'Marina Walk, Dubai Marina', 'Dubai', 'UAE',
 3, 1, 3, 0.800, 1, 1000.00, 'AED', 100.00, 104.00, 'paid', 'delivered', 4, 'high', 2);

-- =============================================
-- SHIPMENT EVENTS
-- =============================================

INSERT INTO shipment_events (shipment_id, status, title, description, location, created_by) VALUES
(1, 'pending', 'Shipment Created', 'Shipment has been created and is awaiting pickup', 'Dubai Main Hub', 2),
(1, 'confirmed', 'Shipment Confirmed', 'Shipment has been confirmed and scheduled for pickup', 'Dubai Main Hub', 4),
(1, 'picked_up', 'Package Picked Up', 'Package has been collected from sender', 'Marina Walk, Dubai Marina', 4),
(1, 'in_transit', 'In Transit', 'Package is on its way to destination', 'Dubai-Abu Dhabi Highway', 4),

(2, 'pending', 'Shipment Created', 'Shipment has been created and is awaiting confirmation', 'Abu Dhabi Branch', 3),

(3, 'pending', 'Shipment Created', 'Shipment has been created and is awaiting pickup', 'Sharjah Branch', 2),
(3, 'confirmed', 'Shipment Confirmed', 'Shipment has been confirmed and scheduled for pickup', 'Sharjah Branch', 4),
(3, 'picked_up', 'Package Picked Up', 'Package has been collected from sender', 'Al Majaz Waterfront, Sharjah', 4),
(3, 'in_transit', 'In Transit', 'Package is on its way to destination', 'Sharjah-Dubai Route', 4),
(3, 'out_for_delivery', 'Out for Delivery', 'Package is out for delivery', 'Dubai Marina Area', 4),
(3, 'delivered', 'Package Delivered', 'Package has been successfully delivered', 'Marina Walk, Dubai Marina', 4);

-- =============================================
-- TRANSACTIONS
-- =============================================

INSERT INTO transactions (uuid, reference_number, user_id, branch_id, shipment_id, type, amount, currency, payment_method_id, status, description, processed_by) VALUES
(UUID(), 'TXN001', NULL, 1, 1, 'payment', 57.50, 'AED', 2, 'completed', 'Payment for shipment ZS2024001001', 2),
(UUID(), 'TXN002', NULL, 3, 3, 'payment', 104.00, 'AED', 1, 'completed', 'Cash payment for shipment ZS2024001003', 4);

-- =============================================
-- SUPPORT TICKETS
-- =============================================

INSERT INTO support_tickets (ticket_number, user_id, branch_id, shipment_id, subject, description, category, priority, status, customer_name, customer_email, customer_phone) VALUES
('TKT001', NULL, 1, 1, 'Delivery Delay Inquiry', 'Customer inquiring about delayed delivery for shipment ZS2024001001', 'shipment', 'normal', 'open', 'Maria Garcia', '<EMAIL>', '+971-50-333-4444'),
('TKT002', NULL, 2, NULL, 'Payment Issue', 'Customer having trouble with online payment', 'payment', 'high', 'in_progress', 'Ahmed Al-Mahmoud', '<EMAIL>', '+971-50-555-6666');

-- =============================================
-- SYSTEM SETTINGS
-- =============================================

INSERT INTO settings (key_name, value, type, group_name, description, is_public) VALUES
('site_name', 'ZamSend Admin Panel', 'string', 'general', 'Website name', TRUE),
('site_logo', '/assets/images/logo.png', 'file', 'general', 'Site logo path', TRUE),
('default_currency', 'AED', 'string', 'general', 'Default currency code', TRUE),
('timezone', 'Asia/Dubai', 'string', 'general', 'Default timezone', FALSE),
('items_per_page', '25', 'number', 'general', 'Default pagination limit', FALSE),
('enable_2fa', 'true', 'boolean', 'security', 'Enable two-factor authentication', FALSE),
('session_timeout', '3600', 'number', 'security', 'Session timeout in seconds', FALSE),
('max_login_attempts', '5', 'number', 'security', 'Maximum login attempts before lockout', FALSE),
('email_from_address', '<EMAIL>', 'string', 'email', 'Default from email address', FALSE),
('email_from_name', 'ZamSend', 'string', 'email', 'Default from name', FALSE);

-- =============================================
-- LANGUAGES
-- =============================================

INSERT INTO languages (name, code, flag, is_default, is_active) VALUES
('English', 'en', '🇺🇸', TRUE, TRUE),
('Arabic', 'ar', '🇦🇪', FALSE, TRUE),
('French', 'fr', '🇫🇷', FALSE, FALSE);

-- =============================================
-- EMAIL TEMPLATES
-- =============================================

INSERT INTO email_templates (name, subject, body, variables) VALUES
('shipment_created', 'Shipment Created - {{tracking_number}}', 
 '<h2>Your shipment has been created</h2><p>Dear {{recipient_name}},</p><p>Your shipment with tracking number <strong>{{tracking_number}}</strong> has been created and will be processed soon.</p><p>You can track your shipment at: <a href="{{tracking_url}}">{{tracking_url}}</a></p><p>Best regards,<br>ZamSend Team</p>', 
 '["tracking_number", "recipient_name", "tracking_url"]'),
('shipment_delivered', 'Package Delivered - {{tracking_number}}', 
 '<h2>Your package has been delivered</h2><p>Dear {{recipient_name}},</p><p>Your shipment with tracking number <strong>{{tracking_number}}</strong> has been successfully delivered.</p><p>Thank you for choosing ZamSend!</p><p>Best regards,<br>ZamSend Team</p>', 
 '["tracking_number", "recipient_name"]');

-- =============================================
-- FAQ
-- =============================================

INSERT INTO faqs (question, answer, category, sort_order, is_active) VALUES
('How can I track my shipment?', 'You can track your shipment using the tracking number provided in your confirmation email. Visit our tracking page and enter your tracking number.', 'tracking', 1, TRUE),
('What are your delivery timeframes?', 'Standard delivery takes 2-3 business days, Express delivery takes 24 hours, and Same Day delivery is completed within 8 hours.', 'delivery', 2, TRUE),
('What payment methods do you accept?', 'We accept cash on delivery, credit/debit cards, bank transfers, and PayPal payments.', 'payment', 3, TRUE),
('How do I calculate shipping costs?', 'Shipping costs are calculated based on weight, distance, service type, and any additional services requested.', 'pricing', 4, TRUE);

-- =============================================
-- BLOG CATEGORIES
-- =============================================

INSERT INTO blog_categories (name, slug, description) VALUES
('Company News', 'company-news', 'Latest news and updates from ZamSend'),
('Industry Insights', 'industry-insights', 'Insights and trends in the courier industry'),
('Tips & Guides', 'tips-guides', 'Helpful tips and guides for our customers');

-- =============================================
-- SAMPLE BLOG POSTS
-- =============================================

INSERT INTO blog_posts (title, slug, content, excerpt, category_id, author_id, status, meta_title, meta_description, published_at) VALUES
('Welcome to ZamSend Admin Panel', 'welcome-to-zamsend-admin-panel', 
 '<p>We are excited to introduce the new ZamSend Admin Panel, designed to streamline your courier management operations.</p><p>This comprehensive platform provides all the tools you need to manage shipments, track deliveries, handle customer support, and analyze your business performance.</p>', 
 'Introducing the new ZamSend Admin Panel with comprehensive courier management features.', 
 1, 1, 'published', 'Welcome to ZamSend Admin Panel', 'Learn about the new ZamSend Admin Panel features and capabilities', NOW()),
('5 Tips for Efficient Package Delivery', 'tips-efficient-package-delivery', 
 '<p>Efficient package delivery is crucial for customer satisfaction. Here are 5 tips to improve your delivery operations:</p><ol><li>Optimize delivery routes</li><li>Use real-time tracking</li><li>Communicate with customers</li><li>Handle packages with care</li><li>Maintain delivery schedules</li></ol>', 
 'Learn 5 essential tips for improving your package delivery efficiency and customer satisfaction.', 
 3, 2, 'published', '5 Tips for Efficient Package Delivery', 'Improve your delivery operations with these 5 essential tips', NOW());
