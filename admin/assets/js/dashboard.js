// Dashboard Specific JavaScript

document.addEventListener('DOMContentLoaded', function() {
    initStatCounters();
    initCharts();
    initRealTimeUpdates();
    initQuickActions();
});

// Animated Stat Counters
function initStatCounters() {
    const statNumbers = document.querySelectorAll('.stat-number[data-count]');
    
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    statNumbers.forEach(stat => {
        observer.observe(stat);
    });
}

function animateCounter(element) {
    const target = parseInt(element.dataset.count.replace(/,/g, ''));
    const duration = 2000;
    const increment = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        
        // Format number with commas
        element.textContent = Math.floor(current).toLocaleString();
    }, 16);
}

// Initialize Charts
function initCharts() {
    initRevenueChart();
    initStatusChart();
}

// Revenue Chart
function initRevenueChart() {
    const ctx = document.getElementById('revenueChart');
    if (!ctx) return;
    
    const revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
            datasets: [{
                label: 'Revenue (AED)',
                data: [15000, 22000, 18000, 28000],
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    border: {
                        display: false
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: '#f1f5f9'
                    },
                    border: {
                        display: false
                    },
                    ticks: {
                        callback: function(value) {
                            return 'AED ' + value.toLocaleString();
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            elements: {
                point: {
                    hoverBackgroundColor: '#3b82f6'
                }
            }
        }
    });
    
    // Chart period selector
    const periodSelector = document.querySelector('.chart-period');
    if (periodSelector) {
        periodSelector.addEventListener('change', (e) => {
            updateRevenueChart(revenueChart, e.target.value);
        });
    }
}

function updateRevenueChart(chart, period) {
    let newData, newLabels;
    
    switch(period) {
        case '7':
            newLabels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            newData = [3200, 4100, 3800, 4500, 5200, 4800, 3900];
            break;
        case '30':
            newLabels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
            newData = [15000, 22000, 18000, 28000];
            break;
        case '90':
            newLabels = ['Month 1', 'Month 2', 'Month 3'];
            newData = [65000, 78000, 85000];
            break;
        default:
            return;
    }
    
    chart.data.labels = newLabels;
    chart.data.datasets[0].data = newData;
    chart.update('active');
}

// Status Chart (Doughnut)
function initStatusChart() {
    const ctx = document.getElementById('statusChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Delivered', 'In Transit', 'Pending'],
            datasets: [{
                data: [87, 10, 3],
                backgroundColor: [
                    '#3b82f6',
                    '#f59e0b',
                    '#ef4444'
                ],
                borderWidth: 0,
                cutout: '70%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// Real-time Updates
function initRealTimeUpdates() {
    // Simulate real-time updates every 30 seconds
    setInterval(() => {
        updateRecentShipments();
        updateNotifications();
        updateBranchPerformance();
    }, 30000);
    
    // Initial update after 5 seconds
    setTimeout(() => {
        simulateNewShipment();
    }, 5000);
}

function updateRecentShipments() {
    const shipmentList = document.querySelector('.shipment-list');
    if (!shipmentList) return;
    
    // Simulate new shipment
    const newShipment = createShipmentElement({
        trackingNumber: 'ZS' + Date.now(),
        route: 'Dubai → Sharjah',
        time: 'Just now',
        status: 'pending'
    });
    
    // Add to top of list
    shipmentList.insertBefore(newShipment, shipmentList.firstChild);
    
    // Remove last item if more than 4
    const items = shipmentList.querySelectorAll('.shipment-item');
    if (items.length > 4) {
        items[items.length - 1].remove();
    }
    
    // Animate new item
    newShipment.style.opacity = '0';
    newShipment.style.transform = 'translateY(-20px)';
    setTimeout(() => {
        newShipment.style.transition = 'all 0.3s ease';
        newShipment.style.opacity = '1';
        newShipment.style.transform = 'translateY(0)';
    }, 100);
}

function createShipmentElement(shipment) {
    const div = document.createElement('div');
    div.className = 'shipment-item';
    div.innerHTML = `
        <div class="shipment-info">
            <div class="tracking-number">${shipment.trackingNumber}</div>
            <div class="shipment-route">${shipment.route}</div>
            <div class="shipment-time">${shipment.time}</div>
        </div>
        <div class="shipment-status">
            <span class="status-badge ${shipment.status}">${shipment.status.charAt(0).toUpperCase() + shipment.status.slice(1)}</span>
        </div>
        <div class="shipment-actions">
            <button class="action-btn" title="View Details">
                <i class="fas fa-eye"></i>
            </button>
            <button class="action-btn" title="Edit">
                <i class="fas fa-edit"></i>
            </button>
        </div>
    `;
    return div;
}

function simulateNewShipment() {
    // Update stat counters
    const totalShipmentsElement = document.querySelector('.stat-number[data-count="1,247"]');
    if (totalShipmentsElement) {
        const currentValue = parseInt(totalShipmentsElement.textContent.replace(/,/g, ''));
        const newValue = currentValue + 1;
        totalShipmentsElement.textContent = newValue.toLocaleString();
        
        // Animate the change
        totalShipmentsElement.style.transform = 'scale(1.1)';
        totalShipmentsElement.style.color = '#10b981';
        setTimeout(() => {
            totalShipmentsElement.style.transform = 'scale(1)';
            totalShipmentsElement.style.color = '';
        }, 300);
    }
    
    // Add new notification
    addNewNotification({
        icon: 'fas fa-shipping-fast text-blue',
        message: 'New shipment created: ZS' + Date.now(),
        time: 'Just now'
    });
    
    // Show toast notification
    if (window.AdminPanel) {
        window.AdminPanel.showToast('New shipment created successfully!', 'success');
    }
}

function addNewNotification(notification) {
    const notificationList = document.querySelector('.notification-list');
    const notificationBadge = document.querySelector('.notification-badge');
    
    if (!notificationList) return;
    
    const notificationElement = document.createElement('div');
    notificationElement.className = 'notification-item unread';
    notificationElement.innerHTML = `
        <div class="notification-icon">
            <i class="${notification.icon}"></i>
        </div>
        <div class="notification-content">
            <p>${notification.message}</p>
            <span class="notification-time">${notification.time}</span>
        </div>
    `;
    
    // Add to top of list
    notificationList.insertBefore(notificationElement, notificationList.firstChild);
    
    // Update badge
    if (notificationBadge) {
        const currentCount = parseInt(notificationBadge.textContent) || 0;
        notificationBadge.textContent = currentCount + 1;
        notificationBadge.style.display = 'block';
        
        // Animate badge
        notificationBadge.style.transform = 'scale(1.2)';
        setTimeout(() => {
            notificationBadge.style.transform = 'scale(1)';
        }, 200);
    }
}

function updateNotifications() {
    // Simulate updating notification times
    const notificationTimes = document.querySelectorAll('.notification-time');
    notificationTimes.forEach(timeElement => {
        const currentTime = timeElement.textContent;
        if (currentTime.includes('minute')) {
            const minutes = parseInt(currentTime);
            timeElement.textContent = `${minutes + 1} minutes ago`;
        } else if (currentTime === 'Just now') {
            timeElement.textContent = '1 minute ago';
        }
    });
}

function updateBranchPerformance() {
    const performanceBars = document.querySelectorAll('.performance-fill');
    performanceBars.forEach(bar => {
        const currentWidth = parseInt(bar.style.width);
        const change = Math.random() * 10 - 5; // Random change between -5% and +5%
        const newWidth = Math.max(0, Math.min(100, currentWidth + change));
        bar.style.width = newWidth + '%';
    });
}

// Quick Actions
function initQuickActions() {
    const actionItems = document.querySelectorAll('.action-item');
    
    actionItems.forEach(item => {
        item.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Add click animation
            item.style.transform = 'scale(0.95)';
            setTimeout(() => {
                item.style.transform = '';
            }, 150);
            
            // Get action type from href
            const href = item.getAttribute('href');
            const actionType = href ? href.split('/').pop().replace('.html', '') : 'unknown';
            
            // Show loading state
            const icon = item.querySelector('.action-icon');
            const originalIcon = icon.innerHTML;
            icon.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            // Simulate action
            setTimeout(() => {
                icon.innerHTML = originalIcon;
                
                if (window.AdminPanel) {
                    window.AdminPanel.showToast(`Navigating to ${actionType}...`, 'info');
                }
                
                // Navigate after animation
                setTimeout(() => {
                    window.location.href = href;
                }, 500);
            }, 1000);
        });
    });
}

// Performance Monitoring
function initPerformanceMonitoring() {
    // Monitor page load time
    window.addEventListener('load', () => {
        const loadTime = performance.now();
        console.log(`Dashboard loaded in ${Math.round(loadTime)}ms`);
        
        // Send to analytics if needed
        if (loadTime > 3000) {
            console.warn('Dashboard load time is slow:', loadTime);
        }
    });
    
    // Monitor memory usage (if available)
    if ('memory' in performance) {
        setInterval(() => {
            const memory = performance.memory;
            const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
            const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
            
            if (usedMB > 100) {
                console.warn(`High memory usage: ${usedMB}MB / ${totalMB}MB`);
            }
        }, 60000); // Check every minute
    }
}

// Initialize performance monitoring
document.addEventListener('DOMContentLoaded', initPerformanceMonitoring);

// Export dashboard functions
window.Dashboard = {
    updateRecentShipments,
    simulateNewShipment,
    addNewNotification,
    updateBranchPerformance
};
