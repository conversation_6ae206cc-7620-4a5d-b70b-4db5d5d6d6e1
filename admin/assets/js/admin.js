// ZamSend Admin Panel - Main JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all admin panel functionality
    initLoadingScreen();
    initSidebar();
    initDropdowns();
    initMobileMenu();
    initNotifications();
    initSearch();
    initTooltips();
});

// Loading Screen
function initLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    
    // Hide loading screen after page load
    window.addEventListener('load', () => {
        setTimeout(() => {
            loadingScreen.classList.add('hidden');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 500);
        }, 1000);
    });
}

// Sidebar Functionality
function initSidebar() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const navItems = document.querySelectorAll('.nav-item.has-submenu');
    
    // Sidebar toggle
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', () => {
            sidebar.classList.toggle('collapsed');
            
            // Save state to localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });
    }
    
    // Restore sidebar state
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed');
    if (sidebarCollapsed === 'true') {
        sidebar.classList.add('collapsed');
    }
    
    // Submenu functionality
    navItems.forEach(item => {
        const link = item.querySelector('.nav-link');
        const submenu = item.querySelector('.submenu');
        
        if (link && submenu) {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                
                // Close other submenus
                navItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('open');
                    }
                });
                
                // Toggle current submenu
                item.classList.toggle('open');
            });
        }
    });
    
    // Auto-expand submenu if current page is in submenu
    const currentPath = window.location.pathname;
    navItems.forEach(item => {
        const submenuLinks = item.querySelectorAll('.submenu a');
        submenuLinks.forEach(link => {
            if (link.getAttribute('href') && currentPath.includes(link.getAttribute('href'))) {
                item.classList.add('open');
                link.classList.add('active');
            }
        });
    });
}

// Dropdown Functionality
function initDropdowns() {
    const dropdowns = [
        {
            trigger: document.getElementById('notification-btn'),
            menu: document.getElementById('notification-menu')
        },
        {
            trigger: document.getElementById('user-btn'),
            menu: document.getElementById('user-menu')
        }
    ];
    
    dropdowns.forEach(dropdown => {
        if (dropdown.trigger && dropdown.menu) {
            // Toggle dropdown
            dropdown.trigger.addEventListener('click', (e) => {
                e.stopPropagation();
                
                // Close other dropdowns
                dropdowns.forEach(otherDropdown => {
                    if (otherDropdown !== dropdown && otherDropdown.menu) {
                        otherDropdown.menu.classList.remove('show');
                    }
                });
                
                // Toggle current dropdown
                dropdown.menu.classList.toggle('show');
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', () => {
        dropdowns.forEach(dropdown => {
            if (dropdown.menu) {
                dropdown.menu.classList.remove('show');
            }
        });
    });
    
    // Prevent dropdown from closing when clicking inside
    dropdowns.forEach(dropdown => {
        if (dropdown.menu) {
            dropdown.menu.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    });
}

// Mobile Menu
function initMobileMenu() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const sidebar = document.getElementById('sidebar');
    
    if (mobileMenuToggle && sidebar) {
        mobileMenuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('mobile-open');
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', (e) => {
            if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                sidebar.classList.remove('mobile-open');
            }
        });
    }
}

// Notifications
function initNotifications() {
    const markAllReadBtn = document.querySelector('.mark-all-read');
    const notificationItems = document.querySelectorAll('.notification-item');
    const notificationBadge = document.querySelector('.notification-badge');
    
    // Mark all as read
    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', () => {
            notificationItems.forEach(item => {
                item.classList.remove('unread');
            });
            
            // Update badge
            if (notificationBadge) {
                notificationBadge.textContent = '0';
                notificationBadge.style.display = 'none';
            }
            
            // Show success message
            showToast('All notifications marked as read', 'success');
        });
    }
    
    // Individual notification click
    notificationItems.forEach(item => {
        item.addEventListener('click', () => {
            item.classList.remove('unread');
            updateNotificationBadge();
        });
    });
    
    // Update notification badge count
    function updateNotificationBadge() {
        const unreadCount = document.querySelectorAll('.notification-item.unread').length;
        if (notificationBadge) {
            notificationBadge.textContent = unreadCount;
            notificationBadge.style.display = unreadCount > 0 ? 'block' : 'none';
        }
    }
}

// Search Functionality
function initSearch() {
    const searchInput = document.querySelector('.search-input');
    const searchBtn = document.querySelector('.search-btn');
    
    if (searchInput && searchBtn) {
        // Search on button click
        searchBtn.addEventListener('click', performSearch);
        
        // Search on Enter key
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // Auto-complete functionality (placeholder)
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();
            if (query.length > 2) {
                // Implement auto-complete logic here
                console.log('Searching for:', query);
            }
        });
    }
    
    function performSearch() {
        const query = searchInput.value.trim();
        if (query) {
            // Implement search logic here
            console.log('Performing search for:', query);
            showToast(`Searching for "${query}"...`, 'info');
        }
    }
}

// Tooltips
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[title]');
    
    tooltipElements.forEach(element => {
        const title = element.getAttribute('title');
        if (title) {
            element.removeAttribute('title');
            element.setAttribute('data-tooltip', title);
            
            // Create tooltip element
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = title;
            document.body.appendChild(tooltip);
            
            // Show tooltip on hover
            element.addEventListener('mouseenter', (e) => {
                const rect = element.getBoundingClientRect();
                tooltip.style.left = rect.left + (rect.width / 2) + 'px';
                tooltip.style.top = rect.top - 35 + 'px';
                tooltip.classList.add('show');
            });
            
            // Hide tooltip
            element.addEventListener('mouseleave', () => {
                tooltip.classList.remove('show');
            });
        }
    });
}

// Alert/System Alerts
function initAlerts() {
    const alertDismissButtons = document.querySelectorAll('.alert-dismiss');
    const dismissAllBtn = document.querySelector('.dismiss-all-btn');
    
    // Individual alert dismiss
    alertDismissButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            const alertItem = btn.closest('.alert-item');
            if (alertItem) {
                alertItem.style.animation = 'slideOut 0.3s ease forwards';
                setTimeout(() => {
                    alertItem.remove();
                }, 300);
            }
        });
    });
    
    // Dismiss all alerts
    if (dismissAllBtn) {
        dismissAllBtn.addEventListener('click', () => {
            const alertItems = document.querySelectorAll('.alert-item');
            alertItems.forEach((item, index) => {
                setTimeout(() => {
                    item.style.animation = 'slideOut 0.3s ease forwards';
                    setTimeout(() => {
                        item.remove();
                    }, 300);
                }, index * 100);
            });
        });
    }
}

// Toast Notifications
function showToast(message, type = 'info', duration = 3000) {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas fa-${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="toast-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // Add to page
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // Auto hide
    setTimeout(() => {
        hideToast(toast);
    }, duration);
    
    // Manual close
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.addEventListener('click', () => {
        hideToast(toast);
    });
    
    function hideToast(toastElement) {
        toastElement.classList.remove('show');
        setTimeout(() => {
            if (toastElement.parentNode) {
                toastElement.parentNode.removeChild(toastElement);
            }
        }, 300);
    }
}

function getToastIcon(type) {
    const icons = {
        success: 'check-circle',
        error: 'exclamation-circle',
        warning: 'exclamation-triangle',
        info: 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Utility Functions
function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

function formatCurrency(amount, currency = 'AED') {
    return new Intl.NumberFormat('en-AE', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

function formatDate(date, options = {}) {
    const defaultOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(new Date(date));
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// Export functions for use in other files
window.AdminPanel = {
    showToast,
    formatNumber,
    formatCurrency,
    formatDate,
    debounce,
    throttle
};

// Initialize alerts when DOM is ready
document.addEventListener('DOMContentLoaded', initAlerts);

// Add CSS for tooltips and toasts
const style = document.createElement('style');
style.textContent = `
    .tooltip {
        position: absolute;
        background: var(--gray-900);
        color: var(--white);
        padding: 0.5rem 0.75rem;
        border-radius: var(--border-radius);
        font-size: 0.75rem;
        white-space: nowrap;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transform: translateX(-50%) translateY(-5px);
        transition: var(--transition);
        pointer-events: none;
    }
    
    .tooltip.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(-50%) translateY(0);
    }
    
    .tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 5px solid transparent;
        border-top-color: var(--gray-900);
    }
    
    .toast {
        position: fixed;
        top: 2rem;
        right: 2rem;
        background: var(--white);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--gray-200);
        padding: 1rem;
        display: flex;
        align-items: center;
        gap: 1rem;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transform: translateX(100%);
        transition: var(--transition);
        min-width: 300px;
        max-width: 400px;
    }
    
    .toast.show {
        opacity: 1;
        visibility: visible;
        transform: translateX(0);
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;
    }
    
    .toast-success {
        border-left: 4px solid var(--success-color);
    }
    
    .toast-error {
        border-left: 4px solid var(--error-color);
    }
    
    .toast-warning {
        border-left: 4px solid var(--warning-color);
    }
    
    .toast-info {
        border-left: 4px solid var(--info-color);
    }
    
    .toast-close {
        background: none;
        border: none;
        color: var(--gray-400);
        cursor: pointer;
        padding: 0.25rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
    }
    
    .toast-close:hover {
        background: var(--gray-100);
        color: var(--gray-600);
    }
    
    @keyframes slideOut {
        to {
            opacity: 0;
            transform: translateX(20px);
        }
    }
`;
document.head.appendChild(style);
