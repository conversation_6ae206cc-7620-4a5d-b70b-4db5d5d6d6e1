/* ZamSend Admin Panel - Main Styles */

/* CSS Variables */
:root {
    /* Colors */
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #06b6d4;
    
    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Layout */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --header-height: 70px;
    --border-radius: 8px;
    --border-radius-lg: 12px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    overflow-x: hidden;
}

/* Admin Panel Layout */
.admin-panel {
    display: flex;
    min-height: 100vh;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--gray-600);
    font-weight: 500;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: var(--header-height);
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-img {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius);
}

.logo-text {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    transition: var(--transition);
}

.sidebar.collapsed .logo-text {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* Sidebar Navigation */
.sidebar-nav {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 0;
}

.nav-menu {
    list-style: none;
}

.nav-item {
    margin: 0.25rem 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.nav-item.active .nav-link {
    background: var(--primary-color);
    color: var(--white);
}

.nav-item.active .nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-dark);
}

.nav-link i {
    width: 20px;
    margin-right: 0.75rem;
    text-align: center;
}

.nav-link span {
    transition: var(--transition);
}

.sidebar.collapsed .nav-link span {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.submenu-arrow {
    margin-left: auto;
    transition: var(--transition);
}

.nav-item.has-submenu.open .submenu-arrow {
    transform: rotate(180deg);
}

/* Submenu */
.submenu {
    list-style: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: var(--gray-50);
}

.nav-item.has-submenu.open .submenu {
    max-height: 300px;
}

.submenu li {
    margin: 0;
}

.submenu a {
    display: block;
    padding: 0.5rem 1.5rem 0.5rem 3.25rem;
    color: var(--gray-600);
    text-decoration: none;
    font-size: 0.875rem;
    transition: var(--transition);
}

.submenu a:hover {
    background: var(--gray-100);
    color: var(--gray-900);
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--gray-200);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    display: flex;
    flex-direction: column;
    transition: var(--transition);
}

.sidebar.collapsed .user-details {
    opacity: 0;
    width: 0;
    overflow: hidden;
}

.user-name {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 0.875rem;
}

.user-role {
    font-size: 0.75rem;
    color: var(--gray-500);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Top Header */
.top-header {
    background: var(--white);
    border-bottom: 1px solid var(--gray-200);
    padding: 0 2rem;
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
}

.mobile-menu-toggle:hover {
    background: var(--gray-100);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Search Box */
.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input {
    width: 300px;
    padding: 0.5rem 1rem;
    padding-right: 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    position: absolute;
    right: 0.5rem;
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0.25rem;
}

/* Notification Dropdown */
.notification-dropdown {
    position: relative;
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--gray-500);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.notification-btn:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.notification-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    background: var(--error-color);
    color: var(--white);
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    min-width: 1.25rem;
    text-align: center;
}

.notification-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 320px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.notification-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notification-header {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.notification-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    border-bottom: 1px solid var(--gray-100);
    transition: var(--transition);
}

.notification-item:hover {
    background: var(--gray-50);
}

.notification-item.unread {
    background: rgba(59, 130, 246, 0.05);
}

.notification-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.notification-icon.text-blue { background: rgba(59, 130, 246, 0.1); color: var(--primary-color); }
.notification-icon.text-green { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
.notification-icon.text-orange { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }
.notification-icon.text-red { background: rgba(239, 68, 68, 0.1); color: var(--error-color); }

.notification-content {
    flex: 1;
}

.notification-content p {
    font-size: 0.875rem;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.notification-footer {
    padding: 0.75rem 1rem;
    border-top: 1px solid var(--gray-200);
    text-align: center;
}

.notification-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
}

/* User Dropdown */
.user-dropdown {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.user-btn:hover {
    background: var(--gray-100);
}

.user-avatar-small {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name-small {
    font-weight: 500;
    color: var(--gray-700);
}

.user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: 1000;
}

.user-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.user-menu-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--gray-700);
    text-decoration: none;
    font-size: 0.875rem;
    transition: var(--transition);
}

.user-menu-item:hover {
    background: var(--gray-50);
}

.user-menu-item.text-red {
    color: var(--error-color);
}

.user-menu-divider {
    height: 1px;
    background: var(--gray-200);
    margin: 0.5rem 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .search-input {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.mobile-open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .search-box {
        display: none;
    }
    
    .top-header {
        padding: 0 1rem;
    }
}

@media (max-width: 640px) {
    .notification-menu,
    .user-menu {
        width: 280px;
        right: -1rem;
    }
    
    .header-right {
        gap: 0.5rem;
    }
}
