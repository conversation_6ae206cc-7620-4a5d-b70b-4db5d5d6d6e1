/* Dashboard Specific Styles */

/* Dashboard Content */
.dashboard-content {
  padding: 2rem;
  flex: 1;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: var(--transition);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: var(--white);
  flex-shrink: 0;
}

.stat-icon.blue {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--primary-dark)
  );
}
.stat-icon.green {
  background: linear-gradient(135deg, var(--success-color), #059669);
}
.stat-icon.orange {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
}
.stat-icon.purple {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}
.stat-icon.red {
  background: linear-gradient(135deg, var(--error-color), #dc2626);
}
.stat-icon.gray {
  background: linear-gradient(135deg, var(--gray-500), var(--gray-600));
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: 0.5rem;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-change.positive {
  color: var(--success-color);
}

.stat-change.negative {
  color: var(--error-color);
}

.stat-change i {
  font-size: 0.625rem;
}

/* Dashboard Grid */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 1.5rem;
}

.dashboard-card {
  background: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.dashboard-card.chart-card {
  grid-column: span 4;
}

.dashboard-card.recent-shipments {
  grid-column: span 6;
}

.dashboard-card.branch-performance {
  grid-column: span 6;
}

.dashboard-card.quick-actions {
  grid-column: span 4;
}

.dashboard-card.system-alerts {
  grid-column: span 8;
}

.card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h3 {
  font-size: 1rem;
  font-weight: 700;
  color: var(--gray-900);
}

.card-content {
  padding: 1rem 1.5rem;
}

/* Chart Controls */
.chart-controls select {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--white);
  color: var(--gray-700);
}

/* Status Legend */
.status-legend {
  margin-top: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--gray-600);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

/* Recent Shipments */
.shipment-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.shipment-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.shipment-item:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
}

.shipment-info {
  flex: 1;
}

.tracking-number {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.shipment-route {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: 0.25rem;
}

.shipment-time {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.shipment-status {
  margin-right: 1rem;
}

.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-badge.delivered {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-badge.in-transit {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.status-badge.delayed {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.shipment-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--gray-100);
  color: var(--gray-600);
  border-radius: var(--border-radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.action-btn:hover {
  background: var(--primary-color);
  color: var(--white);
}

.view-all-btn {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
}

.view-all-btn:hover {
  text-decoration: underline;
}

/* Branch Performance */
.branch-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.branch-item {
  padding: 1rem;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.branch-item:hover {
  background: var(--gray-50);
  border-color: var(--gray-300);
}

.branch-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.branch-name {
  font-weight: 600;
  color: var(--gray-900);
}

.branch-code {
  font-size: 0.75rem;
  color: var(--gray-500);
  background: var(--gray-100);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
}

.branch-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-weight: 600;
  color: var(--gray-900);
  font-size: 0.875rem;
}

.stat-label {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.branch-performance-bar {
  width: 100%;
  height: 4px;
  background: var(--gray-200);
  border-radius: 2px;
  overflow: hidden;
}

.performance-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--success-color),
    var(--primary-color)
  );
  transition: width 0.5s ease;
}

.performance-period {
  font-size: 0.875rem;
  color: var(--gray-500);
  background: var(--gray-100);
  padding: 0.25rem 0.75rem;
  border-radius: var(--border-radius);
}

/* Quick Actions */
.action-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  padding: 1.5rem 1rem;
  border: 1px solid var(--gray-200);
  border-radius: var(--border-radius);
  text-decoration: none;
  color: var(--gray-700);
  transition: var(--transition);
  text-align: center;
}

.action-item:hover {
  background: var(--gray-50);
  border-color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.25rem;
  color: var(--white);
}

.action-item span {
  font-size: 0.875rem;
  font-weight: 500;
}

/* System Alerts */
.alert-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  border-radius: var(--border-radius);
  border-left: 4px solid;
  position: relative;
}

.alert-item.warning {
  background: rgba(245, 158, 11, 0.05);
  border-left-color: var(--warning-color);
}

.alert-item.info {
  background: rgba(6, 182, 212, 0.05);
  border-left-color: var(--info-color);
}

.alert-item.success {
  background: rgba(16, 185, 129, 0.05);
  border-left-color: var(--success-color);
}

.alert-item.error {
  background: rgba(239, 68, 68, 0.05);
  border-left-color: var(--error-color);
}

.alert-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.alert-item.warning .alert-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.alert-item.info .alert-icon {
  background: rgba(6, 182, 212, 0.1);
  color: var(--info-color);
}

.alert-item.success .alert-icon {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.alert-item.error .alert-icon {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-color);
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.alert-message {
  font-size: 0.875rem;
  color: var(--gray-600);
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.alert-time {
  font-size: 0.75rem;
  color: var(--gray-500);
}

.alert-dismiss {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: none;
  border: none;
  color: var(--gray-400);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.alert-dismiss:hover {
  background: var(--gray-100);
  color: var(--gray-600);
}

.dismiss-all-btn {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
}

.dismiss-all-btn:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-card.chart-card {
    grid-column: span 6;
  }

  .dashboard-card.recent-shipments {
    grid-column: span 6;
  }

  .dashboard-card.branch-performance {
    grid-column: span 6;
  }

  .dashboard-card.quick-actions {
    grid-column: span 6;
  }

  .dashboard-card.system-alerts {
    grid-column: span 12;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-card {
    grid-column: span 1 !important;
  }

  .card-header,
  .card-content {
    padding: 1rem;
  }

  .action-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .branch-stats {
    justify-content: space-around;
  }
}
